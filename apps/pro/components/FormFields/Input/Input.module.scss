@use '@styles/variables' as *;
@use '@styles/mixins' as *;

@keyframes errorSlide {
  from {
    height: 0;
    opacity: 0;
    transform: translate3d(0, -15%, 0);
  }

  to {
    height: auto;
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.focus-border {
  width: 100%;
  position: absolute;
  bottom: -1px;

  &::before {
    display: block;
    border-bottom: 3px solid $black;
    content: '';
    transition: transform 300ms cubic-bezier(0, 0, 0.2, 1) 0ms;
    transform: scaleX(0);
  }
}

.field {
  border-bottom: 1px solid $grey-80;
  display: flex;
  height: 50px;
  position: relative;
  width: 100%;
  overflow: hidden;

  &:focus-within,
  &:hover {
    .focus-border::before {
      transform: scaleX(1);
    }
  }

  &.field-error {
    border-bottom: 1px solid $red-80;

    .focus-border::before {
      border-color: $red-80;
    }
  }
}

.input {
  border: 0;
  font-weight: 600;
  outline: none;
  padding: $space-20 0 0;
  width: 100%;
  font-size: 16px;
  line-height: 30px;
}

.input:focus + label {
  transform: scale(0.7) translate3d(0, -40%, 0);
}

.label {
  color: $black;
  display: block;
  line-height: 36px;
  font-weight: 300;
  left: 0;
  margin: auto;
  pointer-events: none;
  position: absolute;
  bottom: 0;
  transform-origin: left;
  transition: transform 100ms ease-in-out;
  text-overflow: ellipsis;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;

  &.float {
    font-size: 20px;
    font-weight: 600;
    bottom: unset;
    transform: scale(0.7) translateY(-50%);
    width: 133%;
  }
}

.hint {
  align-items: center;
  color: $grey-80;
  justify-content: space-between;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  position: absolute;
  top: 54px;
}

.counter {
  color: $black;
}

.error-message {
  position: absolute;
  top: 54px;
  align-items: center;
  animation: errorSlide 200ms ease-in-out;
  color: $red-80;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  z-index: 1;
  line-height: 1;
}

.input-error,
.label-error {
  color: $red-80;
}

.asterisk {
  color: $red-80;

  @include tablet {
    display: none;
  }
}

.disabled {
  opacity: 0.4;

  .input {
    background-color: transparent;
    color: $black;
  }

  .focus-border {
    display: none;
  }
}
