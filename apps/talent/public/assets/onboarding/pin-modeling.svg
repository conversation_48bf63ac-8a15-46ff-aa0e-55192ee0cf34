<svg preserveAspectRatio="xMidYMid meet" viewBox="0 0 169 112" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_362_21)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M56.2081 65.7843C69.3928 61.8295 79 49.6012 79 35.1289C79 17.4558 64.6731 3.12891 47 3.12891C29.3269 3.12891 15 17.4558 15 35.1289C15 49.2943 24.2042 61.31 36.9582 65.5216L46.7144 75.2779L56.2081 65.7843Z" fill="url(#paint0_linear_362_21)"/>
<path d="M43.3927 23.3168L50.8576 23.4778L53.2922 26.0198L58.2689 26.1271C58.5988 26.1343 58.9125 26.2722 59.1407 26.5105C59.369 26.7489 59.4933 27.0682 59.4862 27.3981L59.1104 44.8163C59.1033 45.1463 58.9654 45.4599 58.7271 45.6882C58.4887 45.9165 58.1694 46.0407 57.8394 46.0336L35.4447 45.5505C35.1147 45.5434 34.8011 45.4055 34.5728 45.1672C34.3445 44.9288 34.2202 44.6095 34.2273 44.2795L34.6031 26.8614C34.6102 26.5314 34.7481 26.2178 34.9865 25.9895C35.2248 25.7612 35.5441 25.6369 35.8741 25.6441L40.8507 25.7514L43.3927 23.3168ZM46.6957 43.3038C48.6755 43.3465 50.5912 42.601 52.0214 41.2312C53.4515 39.8615 54.279 37.9797 54.3217 35.9999C54.3644 34.02 53.6189 32.1043 52.2491 30.6742C50.8794 29.2441 48.9976 28.4166 47.0178 28.3739C45.038 28.3312 43.1223 29.0767 41.6921 30.4465C40.262 31.8162 39.4345 33.698 39.3918 35.6778C39.3491 37.6576 40.0946 39.5733 41.4644 41.0035C42.8341 42.4336 44.7159 43.2611 46.6957 43.3038ZM46.7494 40.8155C45.4295 40.787 44.175 40.2354 43.2618 39.2819C42.3487 38.3285 41.8517 37.0514 41.8801 35.7315C41.9086 34.4116 42.4602 33.1571 43.4137 32.2439C44.3671 31.3308 45.6442 30.8337 46.9641 30.8622C48.284 30.8907 49.5385 31.4423 50.4517 32.3957C51.3648 33.3492 51.8618 34.6263 51.8334 35.9462C51.8049 37.2661 51.2533 38.5206 50.2998 39.4338C49.3464 40.3469 48.0693 40.8439 46.7494 40.8155Z" fill="white"/>
</g>
<path d="M7.85352 106L4.93359 95.7168H4.8457C4.98242 97.4681 5.05078 98.8288 5.05078 99.7988V106H1.63281V91.7227H6.76953L9.74805 101.859H9.82617L12.7461 91.7227H17.8926V106H14.3477V99.7402C14.3477 99.4147 14.3509 99.0534 14.3574 98.6562C14.3704 98.2591 14.416 97.2858 14.4941 95.7363H14.4062L11.5254 106H7.85352ZM31.3105 100.443C31.3105 102.253 30.8223 103.666 29.8457 104.682C28.8757 105.691 27.515 106.195 25.7637 106.195C24.084 106.195 22.7461 105.678 21.75 104.643C20.7604 103.607 20.2656 102.208 20.2656 100.443C20.2656 98.64 20.7507 97.2402 21.7207 96.2441C22.6973 95.2415 24.0645 94.7402 25.8223 94.7402C26.9095 94.7402 27.8698 94.9714 28.7031 95.4336C29.5365 95.8958 30.1777 96.5599 30.627 97.4258C31.0827 98.2852 31.3105 99.291 31.3105 100.443ZM24.1426 100.443C24.1426 101.394 24.2695 102.12 24.5234 102.621C24.7773 103.116 25.2038 103.363 25.8027 103.363C26.3952 103.363 26.8118 103.116 27.0527 102.621C27.3001 102.12 27.4238 101.394 27.4238 100.443C27.4238 99.4993 27.3001 98.7865 27.0527 98.3047C26.8053 97.8229 26.3822 97.582 25.7832 97.582C25.1973 97.582 24.7773 97.8229 24.5234 98.3047C24.2695 98.7799 24.1426 99.4928 24.1426 100.443ZM36.9355 106.195C36.1478 106.195 35.4577 105.971 34.8652 105.521C34.2793 105.072 33.8236 104.411 33.498 103.539C33.179 102.667 33.0195 101.648 33.0195 100.482C33.0195 98.6855 33.3809 97.2793 34.1035 96.2637C34.8262 95.248 35.819 94.7402 37.082 94.7402C37.7005 94.7402 38.2409 94.8639 38.7031 95.1113C39.1719 95.3587 39.5983 95.7754 39.9824 96.3613H40.0605C39.9368 95.4954 39.875 94.6296 39.875 93.7637V90.8047H43.7227V106H40.8418L40.0215 104.604H39.875C39.2174 105.665 38.2376 106.195 36.9355 106.195ZM38.498 103.178C39.11 103.178 39.5397 102.986 39.7871 102.602C40.041 102.217 40.1777 101.625 40.1973 100.824V100.521C40.1973 99.5449 40.0573 98.8516 39.7773 98.4414C39.4974 98.0247 39.0579 97.8164 38.459 97.8164C37.9577 97.8164 37.5671 98.054 37.2871 98.5293C37.0137 98.998 36.877 99.6686 36.877 100.541C36.877 101.4 37.0169 102.055 37.2969 102.504C37.5768 102.953 37.9772 103.178 38.498 103.178ZM51.7109 106.195C49.8685 106.195 48.4362 105.707 47.4141 104.73C46.3984 103.747 45.8906 102.351 45.8906 100.541C45.8906 98.6725 46.3626 97.2402 47.3066 96.2441C48.2507 95.2415 49.5983 94.7402 51.3496 94.7402C53.0163 94.7402 54.3021 95.1764 55.207 96.0488C56.1185 96.9147 56.5742 98.1647 56.5742 99.7988V101.498H49.748C49.7741 102.117 49.9987 102.602 50.4219 102.953C50.8516 103.305 51.4342 103.48 52.1699 103.48C52.8405 103.48 53.4557 103.419 54.0156 103.295C54.582 103.165 55.194 102.947 55.8516 102.641V105.385C55.2526 105.691 54.6341 105.902 53.9961 106.02C53.3581 106.137 52.5964 106.195 51.7109 106.195ZM51.4863 97.3379C51.0371 97.3379 50.6595 97.4811 50.3535 97.7676C50.054 98.0475 49.8783 98.4902 49.8262 99.0957H53.1074C53.0944 98.5618 52.9414 98.1354 52.6484 97.8164C52.362 97.4974 51.9746 97.3379 51.4863 97.3379ZM62.5508 106H58.7324V90.8047H62.5508V106ZM69.0352 106H65.2168V94.9355H69.0352V106ZM65.1191 92.1816C65.1191 91.6087 65.2786 91.1823 65.5977 90.9023C65.9167 90.6224 66.431 90.4824 67.1406 90.4824C67.8503 90.4824 68.3678 90.6257 68.6934 90.9121C69.0189 91.1986 69.1816 91.6217 69.1816 92.1816C69.1816 93.3014 68.5013 93.8613 67.1406 93.8613C65.793 93.8613 65.1191 93.3014 65.1191 92.1816ZM78.6836 106V99.9648C78.6836 99.2292 78.5762 98.6758 78.3613 98.3047C78.153 97.9271 77.8145 97.7383 77.3457 97.7383C76.7012 97.7383 76.2357 97.9954 75.9492 98.5098C75.6628 99.0241 75.5195 99.9062 75.5195 101.156V106H71.7012V94.9355H74.582L75.0605 96.3027H75.2852C75.6107 95.7819 76.0599 95.3913 76.6328 95.1309C77.2122 94.8704 77.8763 94.7402 78.625 94.7402C79.849 94.7402 80.7995 95.0951 81.4766 95.8047C82.1602 96.5143 82.502 97.5137 82.502 98.8027V106H78.6836ZM95.5391 94.9355V96.7812L94.0254 97.3379C94.2337 97.7155 94.3379 98.1549 94.3379 98.6562C94.3379 99.8477 93.918 100.772 93.0781 101.43C92.2448 102.087 90.998 102.416 89.3379 102.416C88.9277 102.416 88.6022 102.39 88.3613 102.338C88.2702 102.507 88.2246 102.667 88.2246 102.816C88.2246 103.005 88.3776 103.152 88.6836 103.256C88.9896 103.354 89.39 103.402 89.8848 103.402H91.7207C94.2012 103.402 95.4414 104.447 95.4414 106.537C95.4414 107.885 94.8652 108.933 93.7129 109.682C92.5671 110.43 90.9557 110.805 88.8789 110.805C87.3099 110.805 86.099 110.541 85.2461 110.014C84.3997 109.493 83.9766 108.741 83.9766 107.758C83.9766 106.436 84.7969 105.583 86.4375 105.199C86.099 105.056 85.8027 104.825 85.5488 104.506C85.3014 104.187 85.1777 103.871 85.1777 103.559C85.1777 103.214 85.2721 102.911 85.4609 102.65C85.6497 102.383 86.0436 102.068 86.6426 101.703C86.0697 101.449 85.6172 101.052 85.2852 100.512C84.9596 99.9714 84.7969 99.3138 84.7969 98.5391C84.7969 97.3346 85.207 96.4004 86.0273 95.7363C86.8477 95.0723 88.0195 94.7402 89.543 94.7402C89.7448 94.7402 90.0931 94.763 90.5879 94.8086C91.0827 94.8542 91.4473 94.8965 91.6816 94.9355H95.5391ZM87.1992 107.377C87.1992 107.689 87.3685 107.94 87.707 108.129C88.0456 108.324 88.498 108.422 89.0645 108.422C89.9889 108.422 90.7279 108.305 91.2812 108.07C91.8411 107.842 92.1211 107.546 92.1211 107.182C92.1211 106.889 91.9518 106.68 91.6133 106.557C91.2747 106.439 90.7897 106.381 90.1582 106.381H88.6641C88.2539 106.381 87.9056 106.475 87.6191 106.664C87.3392 106.859 87.1992 107.097 87.1992 107.377ZM88.4004 98.5586C88.4004 99.6914 88.7943 100.258 89.582 100.258C89.9466 100.258 90.2266 100.118 90.4219 99.8379C90.6237 99.5579 90.7246 99.138 90.7246 98.5781C90.7246 97.4323 90.3438 96.8594 89.582 96.8594C88.7943 96.8594 88.4004 97.4258 88.4004 98.5586Z" fill="white"/>
<g filter="url(#filter1_i_362_21)">
<circle cx="78" cy="16" r="16" fill="url(#paint1_linear_362_21)"/>
</g>
<path d="M77.7725 19.9287H76.5693V22H74.5049V19.9287H70.2393V18.459L74.6211 12.0059H76.5693V18.2881H77.7725V19.9287ZM74.5049 18.2881V16.5928C74.5049 16.3102 74.5163 15.9001 74.5391 15.3623C74.5618 14.8245 74.5801 14.5124 74.5938 14.4258H74.5391C74.3704 14.7995 74.1676 15.1641 73.9307 15.5195L72.0986 18.2881H74.5049ZM85.7705 19.9287H84.5674V22H82.5029V19.9287H78.2373V18.459L82.6191 12.0059H84.5674V18.2881H85.7705V19.9287ZM82.5029 18.2881V16.5928C82.5029 16.3102 82.5143 15.9001 82.5371 15.3623C82.5599 14.8245 82.5781 14.5124 82.5918 14.4258H82.5371C82.3685 14.7995 82.1657 15.1641 81.9287 15.5195L80.0967 18.2881H82.5029Z" fill="white"/>
<defs>
<filter id="filter0_i_362_21" x="15" y="3.12891" width="64" height="74.149" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_362_21"/>
</filter>
<filter id="filter1_i_362_21" x="62" y="0" width="32" height="33" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_362_21"/>
</filter>
<linearGradient id="paint0_linear_362_21" x1="15" y1="3.1289" x2="86.6339" y2="66.672" gradientUnits="userSpaceOnUse">
<stop stop-color="#CC165F"/>
<stop offset="1" stop-color="#6D0B32"/>
</linearGradient>
<linearGradient id="paint1_linear_362_21" x1="91.647" y1="-4.23532" x2="68.1176" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#646464"/>
<stop offset="1" stop-color="#312562"/>
</linearGradient>
</defs>
</svg>
