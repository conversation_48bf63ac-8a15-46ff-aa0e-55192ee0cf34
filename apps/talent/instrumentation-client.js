// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  // The DSN tells the SDK where to send the events. If value does not exist, no events will be sent.
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  sendDefaultPii: true,
  ignoreErrors: [
    // Amplitude
    'Amplitude Logger [Error]: Failed to fetch',
    'Amplitude Logger [Error]: Load failed',
    'Amplitude Logger [Error]: NetworkError when attempting to fetch resource',
    'Amplitude Logger [Error]: Event rejected due to exceeded retry count',
    'Error: JSONP request failed.',

    // OneTrust script
    `TypeError: undefined is not an object (evaluating 'r.DomainData')`,
    `TypeError: null is not an object (evaluating 'this.selector.appendChild')`,
    `TypeError: Cannot read properties of undefined (reading 'html')`,
    `TypeError: undefined is not an object (evaluating 'gn.bannerGroup.html')`,

    // Can ignore https://github.com/vercel/next.js/issues/46329 should not be a console error
    // Happens when link clicked multiple times or another link clicked before action finished
    // Slow network, change of mind, slow page
    'Error: Abort fetching component for route',
    'Error: Loading initial props cancelled',
    'Error: Cancel rendering route',

    // GTM
    `ReferenceError: Can't find variable: fbq`,
    'ReferenceError: fbq is not defined',

    // Change of network connection (VPN turn off etc.)
    'TypeError: NetworkError when attempting to fetch resource.',

    // Miscellaneous
    'TypeError: Failed to fetch',
    'TypeError: Load failed',

    // Chrome bug https://issuetracker.google.com/issues/396043331
    `ReferenceError: Can't find variable: gmo`,
  ],

  integrations: [
    Sentry.replayIntegration(),
    Sentry.browserProfilingIntegration(),
    Sentry.captureConsoleIntegration({
      levels: ['error'],
    }),
  ],

  // The maximum number of breadcrumbs sent with events. Defaults to 100.
  maxBreadcrumbs: 100,

  // A list of strings or regex patterns that match transaction names that shouldn't be sent to Sentry.
  ignoreTransactions: [],

  // Sample rate for error events.
  sampleRate: 1.0,

  // Sample rate for other events.
  tracesSampleRate: 1.0,

  // Sample rate for profiling. Performance monitoring. (tracesSampleRate * profilesSampleRate)
  profilesSampleRate: 1.0,

  // Sample rate for session replays. (tracesSampleRate*replaysSessionSampleRate)
  replaysSessionSampleRate: 0,

  // Sample rate for session replays on error events. (sampleRate*replaysOnErrorSampleRate)
  replaysOnErrorSampleRate: 0,
});

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
