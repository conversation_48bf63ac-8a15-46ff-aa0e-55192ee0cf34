'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import cn from 'classnames';
import styles from './SubscriptionPlans.module.scss';
import { PromoCode, PromoToggleButton } from './PromoCode';
import { Amp } from '@services/amp';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import { useAuth } from '@contexts/AuthContext';
import { Button, Radio } from '@components';
import { calculateSavings } from '@utils/calculateSavings';
import { calculatePrice } from '@utils/calculatePrice';
import { xTracking } from '@services/tracking';
import { flattenUtmParams } from '@utils/flattenUtmParams';
import { useNotifications } from '@contexts/NotificationContext';
import { useFeature } from '@contexts/FeatureContext';
import { getPromotedPlan } from '@utils/planSelectHelper';
import GuaranteeIcon from '../../public/assets/icons/icon-guarantee.svg';

const PlanSavings = ({
  baseMonthPrice,
  period,
  price,
  giftCertificatePeriod,
  promotedPeriod,
}) => {
  return (
    <div className={styles['savings-container']}>
      <span>
        <span className={styles['savings-title']}>save</span>
        <span className={styles.savings}>
          {' '}
          {calculateSavings(baseMonthPrice, period, price)}% <sup>1</sup>
        </span>
        {giftCertificatePeriod && promotedPeriod === period && (
          <p className={styles.gift}>+{giftCertificatePeriod} months FREE*</p>
        )}
      </span>
    </div>
  );
};

const PlanDescription = ({
  period,
  giftCertificatePeriod,
  promotedPeriod,
  description,
}) => {
  return (
    <div className={styles.description}>
      {period === 120
        ? description || (
            <>
              Best Value
              <br />
              Unlimited Access
            </>
          )
        : description}
      {giftCertificatePeriod && promotedPeriod === period && (
        <p className={styles.gift}>+{giftCertificatePeriod} months FREE*</p>
      )}
    </div>
  );
};

const SubscriptionPlans = ({
  plans,
  showSale = false,
  styleTheme = 'light',
  amplitudeScope = '',
  displayPeriod = 'month',
  initialSelectedMemberLevelId = null,
  design = null,
  isPreview,
  updateSettings = () => {},
  resetSettings = () => {},
}) => {
  const [selectedMemberLevelId, setSelectedMemberLevelId] = useState(
    initialSelectedMemberLevelId,
  );
  const [showPromoField, setShowPromoField] = useState(false);

  const { paymentMaintenance, promoCodeEnabled } = useFeature();
  const { track } = useAnalytics();
  const { setNotification } = useNotifications();
  const { getIsProfileOutsideUSA } = useAuth();

  const router = useRouter();
  const planSelectRef = useRef(null);
  const planSelectButtonRef = useRef(null);

  const hasPromoPlans = plans.some((plan) => plan.isPromo);
  const isProfileOutsideUSA = getIsProfileOutsideUSA();

  useEffect(() => {
    if (paymentMaintenance) {
      setNotification({
        type: 'error',
        message:
          'We are undergoing scheduled maintenance. During this time payments will be temporarily unavailable for up to few hours.',
      });
    }
  }, [paymentMaintenance]);

  useEffect(() => {
    setSelectedMemberLevelId((previous) =>
      previous === initialSelectedMemberLevelId
        ? previous
        : initialSelectedMemberLevelId,
    );
  }, [initialSelectedMemberLevelId]);

  const getStyleThemeForRadio = () => {
    switch (true) {
      case styleTheme === 'dark':
        return 'white';
      case !!design:
        return 'black';
      default:
        return 'emerald';
    }
  };

  const goToCheckout = () => {
    if (!isPreview) {
      const selectedPlan = plans.find(
        (plan) => plan.member_level_id === selectedMemberLevelId,
      );
      const { promoCode } = selectedPlan;
      const params = promoCode ? `&promo_code=${promoCode}` : '';

      if (selectedPlan['x-tracking']) {
        xTracking(flattenUtmParams(selectedPlan['x-tracking']));
      }

      trackUpgradeSelect(selectedMemberLevelId, GTM_ACTIONS.selected);
      router.push(`/checkout?action=${selectedMemberLevelId}${params}`);
    }
  };

  const trackUpgradeSelect = (memberLevelId, action) => {
    const selectedPlan = plans.find(
      (plan) => plan.member_level_id === memberLevelId,
    );

    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.upgrade,
      action,
      label: `${selectedPlan.period} month${
        selectedPlan.period === 1 ? '' : 's'
      }`,
      value: selectedPlan.price.toFixed(2),
      items: [
        {
          item_id: selectedPlan.member_level_id,
          item_name: `${selectedPlan.period} month${
            selectedPlan.period === 1 ? '' : 's'
          }`,
          price: selectedPlan.price.toFixed(2),
          item_category: 'subscription',
          quantity: 1,
        },
      ],
    });
  };

  const onPlanHover = (period) => {
    if (amplitudeScope === Amp.element.scope.upgrade && !isPreview) {
      Amp.track(Amp.events.elementHovered, {
        name: `plan_item_element`,
        select_plan_period: period,
        scope: amplitudeScope,
        section: Amp.element.section.planSelect,
        type: Amp.element.type.block,
      });
    }
  };

  const onButtonHover = () => {
    if (amplitudeScope === Amp.element.scope.upgrade && !isPreview) {
      Amp.track(Amp.events.elementHovered, {
        name: 'continue_button_element',
        scope: amplitudeScope,
        section: Amp.element.section.planSelect,
        type: Amp.element.type.button,
      });
    }
  };

  const onPromoToggleClick = () =>
    !hasPromoPlans && setShowPromoField((prev) => !prev);

  const {
    promoted_period_free,
    promoted_period,
    promoted_period_badge_text,
    gift_certificate_period,
    badge_text,
    accent_color,
    selected_price_background_color,
  } = design || {};

  const promotedPeriod = getPromotedPlan(plans, promoted_period)?.period;

  const isPromotedPeriodFirst = promotedPeriod === plans?.[0]?.period;

  const offerLabelText = badge_text || 'Best Offer';

  const designStyles =
    (design && {
      '--color': accent_color,
      '--promo-color': accent_color,
      '--price-bg-selected-color': selected_price_background_color,
      '--price-selected-color': accent_color,
      '--price-color': '#383838',
    }) ||
    {};

  const periodBadge =
    promoted_period_free || promoted_period_badge_text
      ? promoted_period_badge_text ||
        `+${promoted_period_free}  month${promoted_period_free > 1 ? 's' : ''} free`
      : '';

  return (
    <div
      ref={planSelectRef}
      className={cn(styles.container, styles[`theme-${styleTheme}`], {
        [styles['container-sale']]: showSale,
      })}
      style={designStyles}
    >
      {plans && (
        <div
          className={cn(styles.plans, {
            [styles['extra-gap']]: !isPromotedPeriodFirst,
          })}
        >
          {plans.map(
            (
              {
                price,
                period,
                base_month_price,
                member_level_id,
                isPromo,
                discount_text,
              },
              i,
            ) => (
              <div
                onClick={
                  member_level_id === selectedMemberLevelId
                    ? () => {
                        goToCheckout();
                      }
                    : () => {
                        setSelectedMemberLevelId(member_level_id);
                      }
                }
                onMouseEnter={() => onPlanHover(period)}
                className={cn(styles.plan, {
                  [styles.promo]: !!isPromo,
                  [styles.selected]: member_level_id === selectedMemberLevelId,
                })}
                key={i}
              >
                {period === 120 && (
                  <img
                    className={styles.icon}
                    alt="icon"
                    src="/assets/upgrade/icon-emoji.webp"
                  />
                )}
                <div
                  className={cn(styles['period-container'], {
                    [styles['period-badge-enabled']]: !!periodBadge,
                  })}
                >
                  {promotedPeriod === period && !hasPromoPlans && (
                    <span className={styles['label-container']}>
                      <span className={styles['before']}>{offerLabelText}</span>
                    </span>
                  )}
                  <Radio
                    className={styles.radio}
                    color={getStyleThemeForRadio()}
                    readOnly
                    checked={member_level_id === selectedMemberLevelId}
                  />
                  {period === 120 && 'Lifetime'}
                  {period !== 120 && period !== promotedPeriod && (
                    <span>{`${period} month${period > 1 ? 's' : ''}`}</span>
                  )}
                  {period !== 120 && period === promotedPeriod && (
                    <div
                      className={
                        periodBadge ? styles['period-badge-container'] : ''
                      }
                    >
                      <span>{`${promoted_period_free ? period - promoted_period_free : period} months`}</span>
                      {!!periodBadge && (
                        <span className={styles['period-badge']}>
                          {periodBadge}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                {period === 120 ? (
                  <div className={styles['price-container']}>
                    <span className={styles['price-period']}>pay once</span>
                    <span className={styles.price}>
                      ${price}
                      {isProfileOutsideUSA && (
                        <span className={styles.currency}>USD</span>
                      )}
                    </span>
                    <span className={styles['price-period']}>
                      enjoy forever
                    </span>
                  </div>
                ) : (
                  <div className={styles['price-container']}>
                    <span className={styles['price-base']}>
                      $
                      {displayPeriod === 'month'
                        ? base_month_price
                        : calculatePrice(base_month_price, 1, displayPeriod)}
                    </span>
                    <span className={styles.price}>
                      $
                      {calculatePrice(
                        price,
                        !!promoted_period_free && period === promotedPeriod
                          ? period - promoted_period_free
                          : period,
                        displayPeriod,
                      )}
                      {isProfileOutsideUSA && (
                        <span className={styles.currency}>USD</span>
                      )}
                    </span>
                    <span className={styles['price-period']}>
                      per {displayPeriod}
                    </span>
                  </div>
                )}
                {period === 120 ? (
                  <PlanDescription
                    period={period}
                    description={discount_text}
                  />
                ) : (
                  <>
                    {discount_text ? (
                      <PlanDescription
                        period={period}
                        giftCertificatePeriod={gift_certificate_period}
                        promotedPeriod={promotedPeriod}
                        description={discount_text}
                      />
                    ) : (
                      <PlanSavings
                        baseMonthPrice={base_month_price}
                        period={period}
                        price={price}
                        giftCertificatePeriod={gift_certificate_period}
                        promotedPeriod={promotedPeriod}
                      />
                    )}
                  </>
                )}
              </div>
            ),
          )}
          <div className={styles['button-container']} ref={planSelectButtonRef}>
            <Button
              label="Continue"
              color={styleTheme === 'dark' ? 'blue' : 'green-gradient'}
              shadow={styleTheme !== 'dark'}
              minWidth="220px"
              onClick={() => {
                goToCheckout();
              }}
              onMouseEnter={onButtonHover}
            />
          </div>
          <div className={styles['refund-info']}>
            <div className={styles['refund-info-first']}>
              <GuaranteeIcon width={16} /> Try Allcasting Risk-Free
            </div>
            <div>Your registration fee is fully refundable</div>
          </div>
          <div className={styles.footer}>
            {isProfileOutsideUSA && <p>All prices are in USD</p>}
            <p>
              Standard full price is{' '}
              <span className={styles['text-accent']}>
                ${plans?.[1]?.base_month_price || 35.99}
                {isProfileOutsideUSA && <>USD</>}
              </span>{' '}
              per month
            </p>
            <p>
              <span className={styles['text-accent']}>
                Billing-Continuous Service
              </span>{' '}
              — All Subscriptions automatically renew until cancelled.
            </p>
            <p>
              <sup>1</sup> This is the percentage savings when compared to the
              full price 1-month plan.
              {promoCodeEnabled && !isPreview && (
                <PromoToggleButton
                  label="Have a Promo Code?"
                  toggleInput={onPromoToggleClick}
                  isInline
                />
              )}
            </p>
            {promoCodeEnabled && !isPreview && showPromoField && (
              <PromoCode
                updateSettings={updateSettings}
                resetSettings={resetSettings}
                amplitudeScope={amplitudeScope}
                plans={plans}
              />
            )}
            {gift_certificate_period > 0 && (
              <p>
                <sup>*</sup> When purchasing a{' '}
                {promoted_period_free
                  ? promotedPeriod - promoted_period_free
                  : promotedPeriod}
                -month subscription, you will receive an exclusive gift coupon
                for {gift_certificate_period} free months. This coupon can be
                shared with a friend or redeemed by you.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionPlans;
