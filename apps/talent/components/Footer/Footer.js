/* eslint-disable @next/next/no-html-link-for-pages */
'use client';
import React, { memo, useState } from 'react';
import styles from './Footer.module.scss';
import IconFacebook from '../../public/assets/icons/social-network/icon-fb-2.svg';
import IconTwitter from '../../public/assets/icons/social-network/icon-twit-2.svg';
import IconYoutube from '../../public/assets/icons/social-network/icon-youtube-2.svg';
import IconInstagram from '../../public/assets/icons/social-network/icon-instagram-2.svg';
import IconLinkedIn from '../../public/assets/icons/social-network/icon-linkedin-2.svg';
import ImageLogo from '../../public/assets/logo/logo-white.svg';
import ImageLogoSmall from '../../public/assets/logo/logo-white-small.svg';
import Link from 'next/link';
import { Contacts, Modal, PrivacyPolicy, TermsOfUse } from '@components';

export default memo(function Footer() {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [showContacts, setShowContacts] = useState(false);

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const toggleShowContacts = (e) => {
    e?.preventDefault();
    setShowContacts(!showContacts);
  };

  return (
    <footer className={styles.footer}>
      <div className={styles.content}>
        <div className={styles.menu}>
          <span className={styles.part}>&nbsp;</span>
          <span className={styles.part}>
            <span className={styles['menu-item']}>
              <strong>solution</strong>
              <Link href={`${process.env.publicUrl}/castingcalls`}>
                casting calls
              </Link>
              <Link href={`${process.env.publicUrl}/talent`}>talent</Link>
            </span>
            <span className={styles['menu-item']}>
              <strong>allcasting</strong>
              <Link href={`${process.env.publicUrl}/about`}>about</Link>
              <Link href={`${process.env.publicUrl}/reviews`}>reviews</Link>
            </span>
            <span className={styles['menu-item']}>
              <strong>resources</strong>
              <Link href={`${process.env.publicUrl}/news`}>news</Link>
              <Link href={`${process.env.publicUrl}/academy`}>lessons</Link>
            </span>
          </span>
        </div>
        <div className={styles.social}>
          <Link
            href="https://www.facebook.com/AllCastingCom"
            aria-label="allcasting Facebook page"
            target="_blank"
            rel="nofollow"
          >
            <IconFacebook />
          </Link>
          <Link
            href="https://twitter.com/AllCastingCom"
            aria-label="allcasting Twitter page"
            target="_blank"
            rel="nofollow"
          >
            <IconTwitter />
          </Link>
          <Link
            href="https://www.youtube.com/channel/UCWQR_N68fck1Ll3wXdhi4Fg"
            aria-label="allcasting YouTube page"
            target="_blank"
            rel="nofollow"
          >
            <IconYoutube />
          </Link>
          <Link
            href="https://www.instagram.com/allcastingcom"
            target="_blank"
            rel="nofollow"
            aria-label="allcasting Instagram page"
          >
            <IconInstagram />
          </Link>
          <Link
            href="https://www.linkedin.com/company/11749633"
            target="_blank"
            rel="nofollow"
            aria-label="allcasting LinkedIn page"
          >
            <IconLinkedIn />
          </Link>
        </div>
        <div className={styles.section}>
          <span className={styles.part}>
            <ImageLogo className={styles['logo-big']} />
            <ImageLogoSmall className={styles['logo-small']} />
          </span>
          <span className={styles.part}>
            <span data-nosnippet="true">
              <p>
                Disclaimer: allcasting is not a talent agency, employer or a
                talent scout; the site is only a venue. AllCasting does not
                promise or facilitate employment. The number of casting calls
                available varies by location, roles available and the level of
                experience required. As with any business, results may vary, and
                will be based on individual capacity, experience, expertise, and
                level of desire. There are no guarantees concerning the level of
                income the user may experience.
              </p>
              <p>
                Trademark Disclaimer: Any product names, logos, brands, and
                other trademarks or images featured or referred to within the{' '}
                <a href={`${process.env.publicUrl}`}>allcasting.com</a> website
                are the property of their respective trademark holders. These
                trademark holders are not affiliated with{' '}
                <a href={`${process.env.publicUrl}`}>allcasting.com</a>, our
                services, or our websites. They do not sponsor or endorse{' '}
                <a href={`${process.env.publicUrl}`}>allcasting.com</a> or any
                of our online products.
              </p>
            </span>
          </span>
        </div>
        <div className={styles.navigation}>
          <div className={styles['navigation-content']}>
            <div className={styles['navigation-content-links']}>
              <ul>
                <li>
                  <a
                    onClick={toggleShowTerms}
                    href={`${process.env.publicUrl}/terms-of-use`}
                    className={styles['legal-content-links-action']}
                  >
                    Terms of Use
                  </a>
                </li>
                <li>
                  <a
                    onClick={toggleShowPrivacyPolicy}
                    href={`${process.env.publicUrl}/privacy-policy`}
                    className={styles['legal-content-links-action']}
                  >
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <Link
                    href={`${process.env.publicUrl}/dnsmpi`}
                    className={styles['legal-content-links-action']}
                  >
                    DNSMPI
                  </Link>
                </li>
                <li>
                  <a
                    onClick={toggleShowContacts}
                    className={styles['legal-content-links-action']}
                    href={`${process.env.publicUrl}/contact`}
                  >
                    Contact Us
                  </a>
                </li>
              </ul>
              <hr />
              <p> © {new Date().getFullYear()} allcasting.com</p>
            </div>
          </div>
        </div>
      </div>
      {showTerms && (
        <Modal backdropClose onClose={toggleShowTerms}>
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal backdropClose onClose={toggleShowPrivacyPolicy}>
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
      {showContacts && (
        <Modal backdropClose onClose={toggleShowContacts}>
          <Contacts />
        </Modal>
      )}
    </footer>
  );
});
