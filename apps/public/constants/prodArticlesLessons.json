{"href": "https://api.allcasting.com/api/gta/articles?limit=140&categories=lessons", "status": "ok", "total": 139, "items": [{"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-traitors-2026-casting-call-nbc", "id": 925, "slug": "the-traitors-2026-casting-call-nbc", "title": "Upcoming Castings: Civilians Can Now Join The Traitors in 2026", "time": "2025-09-02T02:14:26-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-traitors-2026-casting-call-nbc/comments", "total": 0}, "summary": "The Traitors is back for Season 5 in 2026 — and for the first time, civilians can apply! The hit reality series, hosted by <PERSON>, is moving to NBC with a prize fund of up to $250,000. Casting is open now until March 10, 2026.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4584_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4586_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4586_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4586_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4586_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4586_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [{"id": 12, "question": "What type of show is The Traitors?", "answer": "The Traitor is a reality competition series where the contestants compete in a game against one another, with the main goal being a cash prize."}, {"id": 13, "question": "Where does The Traitors take place?", "answer": "Despite being the United States version of the reality game franchise, all of the seasons thus far have taken place in Scotland."}, {"id": 14, "question": "Which network can I watch The Traitors on?", "answer": "The first four seasons of the reality series aired on Peacock, while the upcoming fifth season will appear on NBC."}, {"id": 15, "question": "When does the casting call for Season 5 begin?", "answer": "The casting call for the fifth season of The Traitor was announced on August 14th, 2025, and is open now."}, {"id": 16, "question": "When does the casting call end?", "answer": "NBC has confirmed that the casting call for Season 5 concludes on March 10, 2026."}], "how_to": null, "author": {"id": 12, "name": "<PERSON>"}, "intro": "The Traitors is back for Season 5 in 2026 — and for the first time, civilians can apply! The hit reality series, hosted by <PERSON>, is moving to NBC with a prize fund of up to $250,000. Casting is open now until March 10, 2026."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/self-tape-audition-tools-techniques", "id": 924, "slug": "self-tape-audition-tools-techniques", "title": "Mastering Self-Tapes: Essential Tools and Techniques for Success", "time": "2025-08-15T10:03:38-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/self-tape-audition-tools-techniques/comments", "total": 0}, "summary": "To stand out in today's digital casting world, actors and models must master self-taping using proper video, audio, and lighting tools. High-quality production shows professionalism and ensures your talent isn’t overshadowed by technical issues.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4579_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4581_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4581_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4581_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4581_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4581_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "To stand out in today's digital casting world, actors and models must master self-taping using proper video, audio, and lighting tools. High-quality production shows professionalism and ensures your talent isn’t overshadowed by technical issues."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/acting-opportunities-in-the-metaverse", "id": 922, "slug": "acting-opportunities-in-the-metaverse", "title": "The Role of Actors in the Metaverse: Opportunities You Might Not Know About", "time": "2025-08-12T09:29:56-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/acting-opportunities-in-the-metaverse/comments", "total": 0}, "summary": "The Metaverse offers a groundbreaking space for actors to explore voice acting, virtual performance, and real-time audience interaction. Free from physical casting limitations, performers can grow creatively while mastering immersive technologies that are shaping the future of entertainment.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4570_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4572_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4572_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4572_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4572_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4572_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "The Metaverse offers a groundbreaking space for actors to explore voice acting, virtual performance, and real-time audience interaction. Free from physical casting limitations, performers can grow creatively while mastering immersive technologies that are shaping the future of entertainment."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-start-commercial-modeling", "id": 923, "slug": "how-to-start-commercial-modeling", "title": "How to Break Into Commercial Modeling: A Beginner’s Guide", "time": "2025-08-01T09:46:30-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-start-commercial-modeling/comments", "total": 0}, "summary": "Commercial modeling offers diverse opportunities across lifestyle, fashion, and product campaigns—but getting started takes more than good looks. From building a portfolio to developing acting skills and growing your digital presence, this guide covers everything beginners need to know.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4573_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4575_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4575_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4575_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4575_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4575_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Commercial modeling offers diverse opportunities across lifestyle, fashion, and product campaigns—but getting started takes more than good looks. From building a portfolio to developing acting skills and growing your digital presence, this guide covers everything beginners need to know."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/top-entertainment-jobs-2025", "id": 918, "slug": "top-entertainment-jobs-2025", "title": "Top 5 Entertainment Industry Jobs That Will Dominate 2025", "time": "2025-07-28T10:41:18-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/top-entertainment-jobs-2025/comments", "total": 0}, "summary": "The entertainment industry is rapidly evolving with new tech-driven roles in AI voice acting, digital modeling, VR/AR acting, and motion capture. Performers who embrace digital skills and immersive technologies will find exciting new job opportunities in 2025 and beyond.", "tldr_summary": "The entertainment industry in 2025 will be dominated by tech-forward roles such as digital content creation, AI voice acting, motion capture performance, VR/AR acting, and digital modeling. As demand grows for talent who can navigate virtual platforms, train AI systems, or perform in immersive digital environments, actors and models who adapt to these technologies will unlock exciting, future-proof career opportunities.", "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4567_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4569_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4569_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4569_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4569_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4569_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "The entertainment industry is rapidly evolving with new tech-driven roles in AI voice acting, digital modeling, VR/AR acting, and motion capture. Performers who embrace digital skills and immersive technologies will find exciting new job opportunities in 2025 and beyond."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/fitness-tips-for-models", "id": 917, "slug": "fitness-tips-for-models", "title": "Fitness Tips for Models: How to Stay Runway Ready", "time": "2025-07-22T00:26:32-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/fitness-tips-for-models/comments", "total": 0}, "summary": "Staying runway-ready takes more than genetics—models need structured fitness routines, balanced nutrition, and strong mental wellness habits. This guide offers practical, proven tips to help models perform at their best both on and off the runway.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4564_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4566_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4566_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4566_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4566_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4566_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Staying runway-ready takes more than genetics—models need structured fitness routines, balanced nutrition, and strong mental wellness habits. This guide offers practical, proven tips to help models perform at their best both on and off the runway."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/lessons-learned-from-top-models-and-actors", "id": 916, "slug": "lessons-learned-from-top-models-and-actors", "title": "From Rejection to Stardom: <PERSON><PERSON> Learned from Top Models and Actors", "time": "2025-07-18T10:17:13-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/lessons-learned-from-top-models-and-actors/comments", "total": 0}, "summary": "Many top actors and models faced rejection early in their careers—but their resilience led to lasting success. Stories from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> show how perseverance and self-belief can turn casting setbacks into stardom.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4561_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4563_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4563_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4563_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4563_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4563_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Many top actors and models faced rejection early in their careers—but their resilience led to lasting success. Stories from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> show how perseverance and self-belief can turn casting setbacks into stardom."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/breaking-into-the-world-of-ai-voice-acting", "id": 915, "slug": "breaking-into-the-world-of-ai-voice-acting", "title": "Breaking Into the World of AI Voice Acting: A New Frontier in Casting", "time": "2025-07-15T10:03:11-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/breaking-into-the-world-of-ai-voice-acting/comments", "total": 0}, "summary": "AI voice acting blends traditional vocal performance with advanced technology, creating fresh opportunities in dubbing, localization, and voice model training. Voice actors who adapt to this new frontier can expand their careers while staying at the cutting edge of entertainment innovation.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4558_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4560_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4560_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4560_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4560_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4560_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "AI voice acting blends traditional vocal performance with advanced technology, creating fresh opportunities in dubbing, localization, and voice model training. Voice actors who adapt to this new frontier can expand their careers while staying at the cutting edge of entertainment innovation."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/10-essential-soft-skills-casting-directors-look-for-in-2025", "id": 914, "slug": "10-essential-soft-skills-casting-directors-look-for-in-2025", "title": "10 Essential Soft Skills Casting Directors Look for in 2025", "time": "2025-07-13T09:49:35-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/10-essential-soft-skills-casting-directors-look-for-in-2025/comments", "total": 0}, "summary": "Casting directors now seek models and actors who bring adaptability, emotional intelligence, and professionalism to the table. These soft skills increase casting success and help performers thrive in fast-paced, collaborative environments.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4552_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4554_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4554_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4554_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4554_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4554_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Casting directors now seek models and actors who bring adaptability, emotional intelligence, and professionalism to the table. These soft skills increase casting success and help performers thrive in fast-paced, collaborative environments."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/modeling-for-sustainable-brands", "id": 913, "slug": "modeling-for-sustainable-brands", "title": "Modeling for Sustainable Brands: Why Eco-Friendly Fashion is the Future", "time": "2025-07-08T09:19:19-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/modeling-for-sustainable-brands/comments", "total": 0}, "summary": "Eco-friendly fashion is transforming the industry with a focus on sustainability, ethics, and conscious consumerism. For models, aligning with these values opens up meaningful, future-focused opportunities.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4549_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4551_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4551_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4551_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4551_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4551_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Eco-friendly fashion is transforming the industry with a focus on sustainability, ethics, and conscious consumerism. For models, aligning with these values opens up meaningful, future-focused opportunities."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/2025-entertainment-highlights-movies-tv-shows-reality-series", "id": 911, "slug": "2025-entertainment-highlights-movies-tv-shows-reality-series", "title": "2025 Entertainment Highlights: The Movies, TV Shows, and Reality Series Everyone is Talking About", "time": "2025-01-04T09:26:11-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/2025-entertainment-highlights-movies-tv-shows-reality-series/comments", "total": 0}, "summary": "Discover the top movies, TV shows, and reality series of 2025, including blockbusters, anticipated sequels, and new must-watch titles shaping pop culture this year!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4545_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4547_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4547_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4547_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4547_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4547_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Discover the top movies, TV shows, and reality series of 2025, including blockbusters, anticipated sequels, and new must-watch titles shaping pop culture this year!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/jobs-in-acting-modeling-and-beyond", "id": 910, "slug": "jobs-in-acting-modeling-and-beyond", "title": "Finding Jobs in the Entertainment Industry: Acting, Modeling, and Beyond", "time": "2024-10-28T09:39:47-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/jobs-in-acting-modeling-and-beyond/comments", "total": 0}, "summary": "Learn the key differences from traditional careers, and get practical tips on building portfolios, applying to casting calls, and networking to secure opportunities in this exciting field.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4542_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4544_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4544_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4544_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4544_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4544_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Learn the key differences from traditional careers, and get practical tips on building portfolios, applying to casting calls, and networking to secure opportunities in this exciting field."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/join-squid-games-season-2", "id": 909, "slug": "join-squid-games-season-2", "title": "Want to Join <PERSON><PERSON> Game Season 2? Here’s What You Need to Know", "time": "2024-10-27T09:18:47-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/join-squid-games-season-2/comments", "total": 0}, "summary": "Get ready for Squid Game: The Challenge Season 2! Discover essential casting tips, eligibility requirements, and strategies to make your application stand out.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4539_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4541_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4541_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4541_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4541_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4541_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Get ready for Squid Game: The Challenge Season 2! Discover essential casting tips, eligibility requirements, and strategies to make your application stand out."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-movie", "id": 905, "slug": "how-to-audition-for-movie", "title": "How to Audition for Movie", "time": "2024-10-09T23:24:02-07:00", "featured": 1, "size": 5, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-movie/comments", "total": 0}, "summary": "Discover how to succeed in movie auditions with expert tips for aspiring actors. Learn how to find the right roles, prepare effectively, and build confidence to impress casting directors.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4529_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4531_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4531_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4531_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4531_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4531_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Discover how to succeed in movie auditions with expert tips for aspiring actors. Learn how to find the right roles, prepare effectively, and build confidence to impress casting directors."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-is-a-casting-call", "id": 904, "slug": "what-is-a-casting-call", "title": "What Is a Casting Call? Everything You Need to Know", "time": "2024-09-12T08:59:15-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-is-a-casting-call/comments", "total": 0}, "summary": "Aspiring actors must familiarize themselves with casting calls, which are essential for landing roles in entertainment projects. The article explains different types of casting calls, where to find them, how to apply, and what steps to take after submission.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4526_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4528_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4528_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4528_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4528_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4528_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Aspiring actors must familiarize themselves with casting calls, which are essential for landing roles in entertainment projects. The article explains different types of casting calls, where to find them, how to apply, and what steps to take after submission."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/a-step-by-step-guide-to-the-love-island-usa-application", "id": 903, "slug": "a-step-by-step-guide-to-the-love-island-usa-application", "title": "Unlock Your Chance to Find Love: A Step-by-Step Guide to the Love Island USA Application", "time": "2024-08-06T03:54:26-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/a-step-by-step-guide-to-the-love-island-usa-application/comments", "total": 0}, "summary": "Love Island is one of the most influential reality show franchises of all time - a cultural phenomenon that millions of fans from all over the world tune in to weekly. Can only chosen ones become “islanders” themselves? Wrong, you can do it too! Continue reading below to learn more.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4523_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4525_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4525_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4525_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4525_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4525_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Love Island is one of the most influential reality show franchises of all time - a cultural phenomenon that millions of fans from all over the world tune in to weekly. Can only chosen ones become “islanders” themselves? Wrong, you can do it too! Continue reading below to learn more."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/inspirational-stories-of-actors-who-made-it-big", "id": 902, "slug": "inspirational-stories-of-actors-who-made-it-big", "title": "From Aspiring to Achieving: Inspirational Stories of Actors Who Made It Big", "time": "2024-07-31T08:49:56-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/inspirational-stories-of-actors-who-made-it-big/comments", "total": 0}, "summary": "Looking for motivation? Inspirational Stories of Actors Who Made It Big dives into real journeys of actors who overcame obstacles to reach stardom.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4519_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4521_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4521_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4521_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4521_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4521_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Looking for motivation? Inspirational Stories of Actors Who Made It Big dives into real journeys of actors who overcame obstacles to reach stardom."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-method-acting", "id": 901, "slug": "the-method-acting", "title": "The Method Acting Revolution: How <PERSON><PERSON><PERSON>'s Techniques Shaped Modern Acting", "time": "2024-07-29T08:37:03-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-method-acting/comments", "total": 0}, "summary": "Method acting, rooted in <PERSON>'s early 20th-century techniques, emphasizes emotional authenticity and character immersion. Despite its controversies and intense demands, it remains influential, with actors like <PERSON> and <PERSON> exemplifying its depth and dedication.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4516_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4518_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4518_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4518_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4518_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4518_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "Method acting, rooted in <PERSON>'s early 20th-century techniques, emphasizes emotional authenticity and character immersion. Despite its controversies and intense demands, it remains influential, with actors like <PERSON> and <PERSON> exemplifying its depth and dedication."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/game-changing-audition-tips", "id": 899, "slug": "game-changing-audition-tips", "title": "10 Game-Changing Audition Tips Every Actor Should Know", "time": "2024-07-08T08:04:57-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/game-changing-audition-tips/comments", "total": 0}, "summary": "Discover essential tips to excel in auditions and land your next acting role. Learn how to change your perspective, embrace tension, establish mutual respect, and prioritize authenticity. <PERSON>ain insights on preparation, building confidence, and embracing diverse roles to enhance your acting career.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4522_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4515_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4515_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4515_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4515_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4515_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Discover essential tips to excel in auditions and land your next acting role. Learn how to change your perspective, embrace tension, establish mutual respect, and prioritize authenticity. <PERSON>ain insights on preparation, building confidence, and embracing diverse roles to enhance your acting career."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/voice-acting-techniques", "id": 900, "slug": "voice-acting-techniques", "title": "The Art of Voice Acting: Techniques and Strategies for Success", "time": "2024-07-02T08:26:24-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/voice-acting-techniques/comments", "total": 0}, "summary": "Voice acting requires mastering script interpretation, characterization, and collaboration, while also maintaining vocal health and continuously improving skills. Aspiring actors should invest in quality recording equipment and actively network to enhance career opportunities in this competitive industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4510_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4512_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4512_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4512_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4512_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4512_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Voice acting requires mastering script interpretation, characterization, and collaboration, while also maintaining vocal health and continuously improving skills. Aspiring actors should invest in quality recording equipment and actively network to enhance career opportunities in this competitive industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/voice-acting-tips", "id": 897, "slug": "voice-acting-tips", "title": "Navigating the World of Voice Acting: Essential Tips for Success", "time": "2024-06-28T06:49:43-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/voice-acting-tips/comments", "total": 0}, "summary": "Voice acting is a dynamic industry that brings characters and stories to life across various mediums. Success in this field requires understanding and developing your voice, studying industry trends, creating a strong portfolio, networking, and tailoring performances to individual client needs.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4504_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4506_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4506_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4506_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4506_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4506_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Voice acting is a dynamic industry that brings characters and stories to life across various mediums. Success in this field requires understanding and developing your voice, studying industry trends, creating a strong portfolio, networking, and tailoring performances to individual client needs."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/tips-for-auditions", "id": 898, "slug": "tips-for-auditions", "title": "Monologue Magic: How to Wow Casting Directors in Your Auditions", "time": "2024-06-18T07:30:33-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/tips-for-auditions/comments", "total": 0}, "summary": "Learn how to impress casting directors by mastering audition monologues, showcasing confidence, and maintaining professionalism. Key tips include punctuality, avoiding unnecessary props or gimmicks, and embracing natural body language to highlight acting skills effectively.", "tldr_summary": "Making a lasting impression on casting directors is crucial for actors. Respecting The Schedule: Arriving on time or early demonstrates professionalism and reliability. Emitting Confidence: A positive mindset and confidence in one's abilities can set candidates apart. Let The Monologue Magic Happen: Avoid gimmicks and focus on showcasing acting range and body language. Staying Professional Until The Very End: Be open to feedback and handle rejection or acceptance with grace.", "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4507_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4509_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4509_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4509_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4509_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4509_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Learn how to impress casting directors by mastering audition monologues, showcasing confidence, and maintaining professionalism. Key tips include punctuality, avoiding unnecessary props or gimmicks, and embracing natural body language to highlight acting skills effectively."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-calls-101-land-your-next-role", "id": 896, "slug": "casting-calls-101-land-your-next-role", "title": "Casting Calls 101: Everything You Need to Know to Land Your Next Role", "time": "2024-06-16T05:56:34-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-calls-101-land-your-next-role/comments", "total": 0}, "summary": "New to casting calls? Our guide, Casting Calls 101: Land Your Next Role, covers everything you need to know—from finding open calls to acing auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4501_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4503_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4503_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4503_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4503_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4503_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "New to casting calls? Our guide, Casting Calls 101: Land Your Next Role, covers everything you need to know—from finding open calls to acing auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-create-ugc-creator-portfolio", "id": 895, "slug": "how-to-create-ugc-creator-portfolio", "title": "How To Create Ugc Content Creator Portfolio To Land More Gigs?", "time": "2024-04-17T11:20:50-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-create-ugc-creator-portfolio/comments", "total": 0}, "summary": "Learn how to create a compelling User-Generated Content (UGC) creator portfolio.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4498_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4500_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4500_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4500_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4500_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4500_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Learn how to create a compelling User-Generated Content (UGC) creator portfolio."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-take-headshot-for-actors", "id": 894, "slug": "how-to-take-headshot-for-actors", "title": "How to Take a Good Headshot for Actors?", "time": "2024-04-17T11:00:21-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-take-headshot-for-actors/comments", "total": 0}, "summary": "Master the art of taking captivating headshots for actors.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4495_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4497_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4497_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4497_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4497_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4497_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Master the art of taking captivating headshots for actors."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/top-10-movies-about-auditions", "id": 893, "slug": "top-10-movies-about-auditions", "title": "Top 10 Movies About Auditions Every Actor Should Watch", "time": "2024-04-16T10:55:40-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/top-10-movies-about-auditions/comments", "total": 0}, "summary": "Explore the captivating world of auditions with our top 10 movie picks!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4492_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4494_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4494_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4494_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4494_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4494_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Explore the captivating world of auditions with our top 10 movie picks!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-ultimate-guide-how-to-become-an-actor", "id": 892, "slug": "the-ultimate-guide-how-to-become-an-actor", "title": "The Ultimate Guide: How to Become an Actor from Scratch", "time": "2024-04-03T11:42:13-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-ultimate-guide-how-to-become-an-actor/comments", "total": 0}, "summary": "Master auditions, refine your skills, and navigate the industry with confidence.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4489_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4491_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4491_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4491_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4491_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4491_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Master auditions, refine your skills, and navigate the industry with confidence."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/social-media-for-actors-complete-guide", "id": 889, "slug": "social-media-for-actors-complete-guide", "title": "Social Media for Actors: Complete Guide", "time": "2023-12-27T01:46:16-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/social-media-for-actors-complete-guide/comments", "total": 0}, "summary": "Social media is crucial for actors and models, offering a platform to connect with industry leaders and showcase talent. Effective profiles should include professional headshots, clear bios, and engaging content. Platforms like Instagram, LinkedIn, and TikTok are valuable for networking and finding opportunities.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4466_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4468_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4468_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4468_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4468_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4468_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Social media is crucial for actors and models, offering a platform to connect with industry leaders and showcase talent. Effective profiles should include professional headshots, clear bios, and engaging content. Platforms like Instagram, LinkedIn, and TikTok are valuable for networking and finding opportunities."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-set-up-a-voiceover-studio-at-home", "id": 883, "slug": "how-to-set-up-a-voiceover-studio-at-home", "title": "How to Set up a Voiceover Studio at Home?", "time": "2023-12-15T06:41:52-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-set-up-a-voiceover-studio-at-home/comments", "total": 0}, "summary": "Start voicing your dreams today.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4463_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4465_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4465_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4465_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4465_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4465_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Start voicing your dreams today."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-scare-actor", "id": 882, "slug": "how-to-become-a-scare-actor", "title": "How to Become a Scare Actor?", "time": "2023-12-15T05:46:37-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-scare-actor/comments", "total": 0}, "summary": "Scare acting is a unique and thrilling role that involves scaring visitors at haunted attractions. It requires creativity, enthusiasm, and the ability to stay in character. Scare actors typically earn around $16.62 per hour and do not need formal acting credentials.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4459_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4461_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4461_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4461_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4461_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4461_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Scare acting is a unique and thrilling role that involves scaring visitors at haunted attractions. It requires creativity, enthusiasm, and the ability to stay in character. Scare actors typically earn around $16.62 per hour and do not need formal acting credentials."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-cry-on-command-for-acting", "id": 881, "slug": "how-to-cry-on-command-for-acting", "title": "How to Cry on Command for Acting?", "time": "2023-12-15T04:21:00-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-cry-on-command-for-acting/comments", "total": 0}, "summary": "Unlocking the actor's emotional toolkit: Explore methods to cry on command.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4454_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4456_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4456_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4456_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4456_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4456_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Unlocking the actor's emotional toolkit: Explore methods to cry on command."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-acting-experience", "id": 880, "slug": "how-to-get-acting-experience", "title": "How to Get Acting Experience?", "time": "2023-12-15T03:05:46-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-acting-experience/comments", "total": 0}, "summary": "Start your journey to becoming a seasoned actor today.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4451_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4453_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4453_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4453_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4453_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4453_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Start your journey to becoming a seasoned actor today."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/wednesday-season-2-auditions-how-to-get-casting", "id": 877, "slug": "wednesday-season-2-auditions-how-to-get-casting", "title": "Wednesday Season 2 Auditions: How to Get Casting?", "time": "2023-12-07T07:29:58-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/wednesday-season-2-auditions-how-to-get-casting/comments", "total": 0}, "summary": "Wednesday Season 2 Auditions: Your ticket to the Addams Family.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4443_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4445_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4445_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4445_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4445_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4445_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Wednesday Season 2 Auditions: Your ticket to the Addams Family."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-walking-dead-casting-calls", "id": 878, "slug": "the-walking-dead-casting-calls", "title": "The Walking Dead Casting Calls: How to Get Cast?", "time": "2023-12-07T06:18:29-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-walking-dead-casting-calls/comments", "total": 0}, "summary": "Discover the path to landing a role in The Walking Dead casting calls.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4446_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4448_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4448_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4448_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4448_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4448_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Discover the path to landing a role in The Walking Dead casting calls."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/difference-between-union-nonunion", "id": 156, "slug": "difference-between-union-nonunion", "title": "Difference Between Union and Non-Union", "time": "2023-12-06T02:03:30-08:00", "featured": 1, "size": 4, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/difference-between-union-nonunion/comments", "total": 0}, "summary": "Choosing between union and non-union labor is a key decision.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4439_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4441_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4441_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4441_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4441_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4441_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Choosing between union and non-union labor is a key decision."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-calls-for-the-batman-part-2", "id": 876, "slug": "casting-calls-for-the-batman-part-2", "title": "Casting Calls For The Batman Part 2 - How to Become a Star?", "time": "2023-12-06T00:33:51-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-calls-for-the-batman-part-2/comments", "total": 0}, "summary": "Discover the thrilling world of 'The Batman Part 2 ➡️", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4436_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4438_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4438_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4438_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4438_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4438_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Discover the thrilling world of 'The Batman Part 2 ➡️"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/best-online-acting-classes", "id": 732, "slug": "best-online-acting-classes", "title": "Best Online Acting Classes", "time": "2023-11-15T05:36:14-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/best-online-acting-classes/comments", "total": 0}, "summary": "Waking up at 6AM to catch a bus for university to learn about acting? Those days are long gone. It's quality over quantity - in the current modern world, you can educate yourself from the comfort of your home and whenever it's convenient for you.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3758_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3722_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3722_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3722_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3722_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3722_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Waking up at 6AM to catch a bus for university to learn about acting? Those days are long gone. It's quality over quantity - in the current modern world, you can educate yourself from the comfort of your home and whenever it's convenient for you."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/stranger-things-casting-call", "id": 875, "slug": "stranger-things-casting-call", "title": "Stranger Things Casting Call - How to Get Cast?", "time": "2023-11-06T03:30:38-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/stranger-things-casting-call/comments", "total": 0}, "summary": "Unearth audition tips and embark on a journey to Hawkins, Indiana!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4433_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4435_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4435_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4435_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4435_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4435_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Unearth audition tips and embark on a journey to Hawkins, Indiana!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/harry-potter-casting-call", "id": 874, "slug": "harry-potter-casting-call", "title": "<PERSON> Casting Call - How to Get a Role in a TV Series?", "time": "2023-11-06T00:26:31-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/harry-potter-casting-call/comments", "total": 0}, "summary": "Learn how to secure a role in the Harry Potter TV series.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4430_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4432_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4432_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4432_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4432_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4432_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Learn how to secure a role in the Harry Potter TV series."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/love-island-usa-casting", "id": 871, "slug": "love-island-usa-casting", "title": "Love Island USA Casting", "time": "2023-11-03T08:03:34-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/love-island-usa-casting/comments", "total": 0}, "summary": "Explore the casting details and requirements for Love Island USA", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4413_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4417_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4417_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4417_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4417_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4417_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Explore the casting details and requirements for Love Island USA"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-respond-to-a-casting-call-by-email", "id": 869, "slug": "how-to-respond-to-a-casting-call-by-email", "title": "How to Respond to a Casting Call by Email?", "time": "2023-10-23T02:59:21-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-respond-to-a-casting-call-by-email/comments", "total": 0}, "summary": "Learn the dos and don'ts, and craft a winning email submission to casting calls.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4407_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4411_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4411_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4411_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4411_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4411_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Learn the dos and don'ts, and craft a winning email submission to casting calls."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/avengers-the-kang-dynasty", "id": 873, "slug": "avengers-the-kang-dynasty", "title": "Avengers: The Kang Dynasty", "time": "2023-10-23T02:30:00-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/avengers-the-kang-dynasty/comments", "total": 0}, "summary": "Explore 'Avengers: The Kang Dynasty' - the next big Marvel saga.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4425_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4429_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4429_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4429_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4429_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4429_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Explore 'Avengers: The Kang Dynasty' - the next big Marvel saga."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/good-questions-to-ask-casting-director", "id": 867, "slug": "good-questions-to-ask-casting-director", "title": "Good Questions to Ask Casting Directors", "time": "2023-10-23T02:11:04-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/good-questions-to-ask-casting-director/comments", "total": 0}, "summary": "Explore a curated list of good questions to ask casting directors and elevate your audition game.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4401_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4405_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4405_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4405_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4405_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4405_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Explore a curated list of good questions to ask casting directors and elevate your audition game."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/auditioning", "id": 26, "slug": "auditioning", "title": "How to Prepare For an Audition?", "time": "2023-10-20T05:39:06-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/auditioning/comments", "total": 0}, "summary": "Uncover the essential steps and tips for effective audition preparation.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4395_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4399_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4399_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4399_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4399_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4399_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Uncover the essential steps and tips for effective audition preparation."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/acting-manager-vs-agent", "id": 865, "slug": "acting-manager-vs-agent", "title": "Acting Manager vs. Agent", "time": "2023-10-20T04:34:06-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/acting-manager-vs-agent/comments", "total": 0}, "summary": "Talent agents and managers have distinct roles in advancing an actor's career. While agents focus on securing work and negotiating contracts, managers guide overall career development and provide personalized advice. Understanding these differences is crucial for building an effective representation team.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4419_story_pic_sizeStory.webp", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4423_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4423_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4423_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4423_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4423_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Talent agents and managers have distinct roles in advancing an actor's career. While agents focus on securing work and negotiating contracts, managers guide overall career development and provide personalized advice. Understanding these differences is crucial for building an effective representation team."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/complete-guide-to-demo-reels-for-actors", "id": 574, "slug": "complete-guide-to-demo-reels-for-actors", "title": "The Complete Guide to <PERSON><PERSON> For Actors", "time": "2023-10-13T23:52:18-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/complete-guide-to-demo-reels-for-actors/comments", "total": 0}, "summary": "Learn everything you need to know about demo reels. What are they? How to make them perfect? What to do if you don't have one? All that and more - read on.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4375_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4379_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4379_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4379_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4379_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4379_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Learn everything you need to know about demo reels. What are they? How to make them perfect? What to do if you don't have one? All that and more - read on."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-do-you-write-an-acting-cover-letter", "id": 863, "slug": "how-do-you-write-an-acting-cover-letter", "title": "How Do You Write an Acting Cover Letter?", "time": "2023-10-13T07:38:55-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-do-you-write-an-acting-cover-letter/comments", "total": 0}, "summary": "Unlock the secrets of writing a winning acting cover letter", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4381_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4385_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4385_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4385_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4385_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4385_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Unlock the secrets of writing a winning acting cover letter"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-much-do-acting-agents-cost", "id": 861, "slug": "how-much-do-acting-agents-cost", "title": "How Much Do Acting Agents Cost?", "time": "2023-10-13T02:30:33-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-much-do-acting-agents-cost/comments", "total": 0}, "summary": "Your guide to understanding the cost of representation.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4365_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4369_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4369_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4369_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4369_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4369_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Your guide to understanding the cost of representation."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-is-cold-reading", "id": 859, "slug": "what-is-cold-reading", "title": "What Is Cold Reading?", "time": "2023-10-05T00:11:51-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-is-cold-reading/comments", "total": 0}, "summary": "Cold reading: Acting without preparation. Learn more about this essential skill for actors ➡️.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4361_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4363_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4363_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4363_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4363_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4363_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Cold reading: Acting without preparation. Learn more about this essential skill for actors ➡️."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-come-up-with-a-stage-name", "id": 855, "slug": "how-to-come-up-with-a-stage-name", "title": "How to Come up With a Stage Name?", "time": "2023-09-12T06:12:37-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-come-up-with-a-stage-name/comments", "total": 0}, "summary": "Uncover the art of crafting a unique stage name that represents your identity as an artist.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4343_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4347_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4347_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4347_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4347_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4347_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Uncover the art of crafting a unique stage name that represents your identity as an artist."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-hallmark-movies", "id": 853, "slug": "how-to-audition-for-hallmark-movies", "title": "How to Audition for Hallmark Movies?", "time": "2023-09-08T07:16:04-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-hallmark-movies/comments", "total": 0}, "summary": "Dive into the audition process and gain key insights to land your spot in a Hallmark.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4335_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4339_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4339_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4339_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4339_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4339_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Dive into the audition process and gain key insights to land your spot in a Hallmark."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-does-slate-mean-in-an-audition", "id": 851, "slug": "what-does-slate-mean-in-an-audition", "title": "What Does <PERSON><PERSON> Mean in an Audition?", "time": "2023-09-08T05:24:09-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-does-slate-mean-in-an-audition/comments", "total": 0}, "summary": "A slate in acting is a brief introduction before an audition where an actor presents their name, age, and role to the casting team. It's crucial for making a strong first impression and differentiating the actor from their character. Tips include balancing personal and character introduction, being concise, and maintaining professionalism.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4327_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4331_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4331_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4331_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4331_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4331_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "A slate in acting is a brief introduction before an audition where an actor presents their name, age, and role to the casting team. It's crucial for making a strong first impression and differentiating the actor from their character. Tips include balancing personal and character introduction, being concise, and maintaining professionalism."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-avatar-3", "id": 845, "slug": "how-to-audition-for-avatar-3", "title": "How to Audition for Avatar 3?", "time": "2023-08-14T00:33:21-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-avatar-3/comments", "total": 0}, "summary": "<PERSON>'s Avatar 3 is set for release on December 19, 2025, introducing a new Na'vi clan known as The Ash People. Casting calls are anticipated in New Zealand and Los Angeles. Follow updates from production studios and casting director <PERSON><PERSON><PERSON> for opportunities.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4297_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4301_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4301_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4301_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4301_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4301_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "<PERSON>'s Avatar 3 is set for release on December 19, 2025, introducing a new Na'vi clan known as The Ash People. Casting calls are anticipated in New Zealand and Los Angeles. Follow updates from production studios and casting director <PERSON><PERSON><PERSON> for opportunities."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/dirty-dancing-2024-casting-call", "id": 843, "slug": "dirty-dancing-2024-casting-call", "title": "Dirty Dancing 2024 Casting Call - How to Get the Role?", "time": "2023-08-13T23:56:19-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/dirty-dancing-2024-casting-call/comments", "total": 0}, "summary": "Lionsgate is set to release a sequel to the iconic 1987 film \"Dirty Dancing\" on February 9, 2024, with <PERSON> reprising her role as '<PERSON>.' The film will be directed by <PERSON>, with casting led by <PERSON>. Filming is expected to occur in Asheville, North Carolina.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4291_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4295_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4295_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4295_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4295_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4295_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Lionsgate is set to release a sequel to the iconic 1987 film \"Dirty Dancing\" on February 9, 2024, with <PERSON> reprising her role as '<PERSON>.' The film will be directed by <PERSON>, with casting led by <PERSON>. Filming is expected to occur in Asheville, North Carolina."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-voice-actor-for-cartoons", "id": 841, "slug": "how-to-become-a-voice-actor-for-cartoons", "title": "How to Become a Voice Actor for Cartoons?", "time": "2023-08-11T04:34:02-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-voice-actor-for-cartoons/comments", "total": 0}, "summary": "✨ To excel in cartoon voice acting, master vocal techniques, and auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4285_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4289_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4289_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4289_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4289_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4289_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "✨ To excel in cartoon voice acting, master vocal techniques, and auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-much-do-commercial-models-make-per-shoot", "id": 839, "slug": "how-much-do-commercial-models-make-per-shoot", "title": "How Much Do Commercial Models Make Per Shoot?", "time": "2023-08-04T02:16:30-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-much-do-commercial-models-make-per-shoot/comments", "total": 0}, "summary": "Webinar with <PERSON><PERSON>: Learn about pay rates per shoot and uncover the factors influencing income ➡️.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4277_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4281_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4281_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4281_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4281_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4281_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Webinar with <PERSON><PERSON>: Learn about pay rates per shoot and uncover the factors influencing income ➡️."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-model-for-shein", "id": 836, "slug": "how-to-become-a-model-for-shein", "title": "How to Become a Model for <PERSON><PERSON>?", "time": "2023-07-13T05:50:29-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-model-for-shein/comments", "total": 0}, "summary": "Navigate the steps and insights to become a face for <PERSON><PERSON> with our comprehensive guide ➡️.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4254_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4258_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4258_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4258_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4258_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4258_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Navigate the steps and insights to become a face for <PERSON><PERSON> with our comprehensive guide ➡️."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/commercial-acting-classes", "id": 834, "slug": "commercial-acting-classes", "title": "Commercial Acting Classes", "time": "2023-07-13T04:06:05-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/commercial-acting-classes/comments", "total": 0}, "summary": "Commercial acting classes provide essential skills for auditioning and booking TV commercials. These classes cover improvisation, on-camera techniques, and voice-over skills, helping actors build confidence and industry knowledge. Choosing the right class based on goals and skill level can enhance career prospects.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4244_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4248_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4248_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4248_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4248_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4248_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Commercial acting classes provide essential skills for auditioning and booking TV commercials. These classes cover improvisation, on-camera techniques, and voice-over skills, helping actors build confidence and industry knowledge. Choosing the right class based on goals and skill level can enhance career prospects."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-best-podcasts-for-actors", "id": 832, "slug": "the-best-podcasts-for-actors", "title": "The Best Podcasts for Actors", "time": "2023-06-29T04:59:25-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-best-podcasts-for-actors/comments", "total": 0}, "summary": "Discover the top shows that inspire, educate, and entertain aspiring and seasoned actors.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4216_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4220_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4220_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4220_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4220_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4220_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Discover the top shows that inspire, educate, and entertain aspiring and seasoned actors."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/minecraft-2025-casting-call", "id": 830, "slug": "minecraft-2025-casting-call", "title": "Minecraft 2025 Casting Call - How to Get the Role?", "time": "2023-06-29T04:02:24-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/minecraft-2025-casting-call/comments", "total": 0}, "summary": "The highly anticipated Minecraft movie is set to release on April 4, 2025, directed by <PERSON>. <PERSON> stars in the live-action film, which follows a young girl's quest to save the Overworld. Filming will occur in Calgary, Alberta, Canada.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4210_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4214_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4214_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4214_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4214_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4214_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "The highly anticipated Minecraft movie is set to release on April 4, 2025, directed by <PERSON>. <PERSON> stars in the live-action film, which follows a young girl's quest to save the Overworld. Filming will occur in Calgary, Alberta, Canada."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-catalog-model", "id": 828, "slug": "how-to-become-a-catalog-model", "title": "How to Become a Catalog Model?", "time": "2023-06-29T03:20:42-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-catalog-model/comments", "total": 0}, "summary": "Catalog models promote products like clothing and accessories, differing from editorial models who focus on fashion. Anyone can become a catalog model, but it requires certain qualities such as versatility, photogenic qualities, and professionalism. The average U.S. salary is $75,479 annually.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4204_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4208_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4208_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4208_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4208_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4208_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Catalog models promote products like clothing and accessories, differing from editorial models who focus on fashion. Anyone can become a catalog model, but it requires certain qualities such as versatility, photogenic qualities, and professionalism. The average U.S. salary is $75,479 annually."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-train-your-dragon-2025-casting-call", "id": 826, "slug": "how-to-train-your-dragon-2025-casting-call", "title": "How to Train Your Dragon 2025 Casting Call - How to Get the Role?", "time": "2023-06-29T01:17:46-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-train-your-dragon-2025-casting-call/comments", "total": 0}, "summary": "DreamWorks and Universal Studios are bringing \"How to Train Your Dragon\" to life in a live-action adaptation, set to release on March 14, 2025. Directed by <PERSON>, the film will feature <PERSON> as <PERSON><PERSON><PERSON> and <PERSON> as <PERSON><PERSON><PERSON>. Filming begins in Los Angeles.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4196_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4200_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4200_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4200_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4200_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4200_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "DreamWorks and Universal Studios are bringing \"How to Train Your Dragon\" to life in a live-action adaptation, set to release on March 14, 2025. Directed by <PERSON>, the film will feature <PERSON> as <PERSON><PERSON><PERSON> and <PERSON> as <PERSON><PERSON><PERSON>. Filming begins in Los Angeles."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-make-a-comp-card-for-a-model", "id": 822, "slug": "how-to-make-a-comp-card-for-a-model", "title": "How to Make a Comp Card for a Model?", "time": "2023-06-08T02:22:49-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-make-a-comp-card-for-a-model/comments", "total": 0}, "summary": "Learn how to create a comp card to showcase your modeling portfolio.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4184_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4188_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4188_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4188_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4188_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4188_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Learn how to create a comp card to showcase your modeling portfolio."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/best-books-for-aspiring-actors", "id": 820, "slug": "best-books-for-aspiring-actors", "title": "The Best Books for Aspiring Actors", "time": "2023-06-08T01:13:31-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/best-books-for-aspiring-actors/comments", "total": 0}, "summary": "Aspiring actors can enhance their craft with essential reading from industry legends. Recommended books include \"Respect for Acting\" by <PERSON><PERSON>, \"An Actor Prepares\" by <PERSON><PERSON><PERSON>, and \"Audition\" by <PERSON>, offering invaluable insights and techniques for success.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4178_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4182_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4182_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4182_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4182_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4182_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Aspiring actors can enhance their craft with essential reading from industry legends. Recommended books include \"Respect for Acting\" by <PERSON><PERSON>, \"An Actor Prepares\" by <PERSON><PERSON><PERSON>, and \"Audition\" by <PERSON>, offering invaluable insights and techniques for success."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/fantastic-four-casting-call-seeking-for-extra", "id": 818, "slug": "fantastic-four-casting-call-seeking-for-extra", "title": "Fantastic Four Casting Call Seeking for Extra", "time": "2023-06-07T06:10:32-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/fantastic-four-casting-call-seeking-for-extra/comments", "total": 0}, "summary": "Marvel Studios is set to reintroduce the Fantastic Four as part of the MCU, with the film scheduled for release on February 14, 2025. Rumors about casting are circulating, with <PERSON><PERSON> speculated to play <PERSON>. Filming is expected to begin in London in January 2024.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4172_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4176_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4176_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4176_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4176_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4176_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "Marvel Studios is set to reintroduce the Fantastic Four as part of the MCU, with the film scheduled for release on February 14, 2025. Rumors about casting are circulating, with <PERSON><PERSON> speculated to play <PERSON>. Filming is expected to begin in London in January 2024."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-improve-acting-skills", "id": 814, "slug": "how-to-improve-acting-skills", "title": "How to Improve Acting Skills?", "time": "2023-06-06T02:16:35-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-improve-acting-skills/comments", "total": 0}, "summary": "Explore proven techniques and exercises to enhance your acting skills.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4160_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4164_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4164_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4164_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4164_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4164_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Explore proven techniques and exercises to enhance your acting skills."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-best-background-for-self-tapes", "id": 812, "slug": "the-best-background-for-self-tapes", "title": "The Best Background for Self-tapes", "time": "2023-06-06T01:12:37-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-best-background-for-self-tapes/comments", "total": 0}, "summary": "Choosing the right background for self-tapes is crucial to complementing your performance. Consider factors like skin tone, genre, and wardrobe when selecting colors such as grey, blue, white, black, or green. Avoid bright colors and ensure your attire contrasts with the background for optimal results.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4154_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4158_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4158_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4158_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4158_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4158_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Choosing the right background for self-tapes is crucial to complementing your performance. Consider factors like skin tone, genre, and wardrobe when selecting colors such as grey, blue, white, black, or green. Avoid bright colors and ensure your attire contrasts with the background for optimal results."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/marvel-deadpool-3-open-casting-call", "id": 810, "slug": "marvel-deadpool-3-open-casting-call", "title": "Marvel' Deadpool 3' Open Casting Call for Everyone", "time": "2023-06-01T06:15:44-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/marvel-deadpool-3-open-casting-call/comments", "total": 0}, "summary": "Unleash your super-acting skills and be part of the adventure!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4146_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4150_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4150_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4150_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4150_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4150_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Unleash your super-acting skills and be part of the adventure!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-choose-a-monologue", "id": 808, "slug": "how-to-choose-a-monologue", "title": "How to Choose a Monologue?", "time": "2023-05-27T03:26:08-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-choose-a-monologue/comments", "total": 0}, "summary": "Explore the art of monologues and learn how to choose the perfect one for your next audition. Discover various types such as soliloquies, dramatic, and comedic monologues, along with expert tips on selecting and performing a captivating piece that showcases your talent.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4134_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4138_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4138_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4138_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4138_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4138_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 12, "name": "<PERSON>"}, "intro": "Explore the art of monologues and learn how to choose the perfect one for your next audition. Discover various types such as soliloquies, dramatic, and comedic monologues, along with expert tips on selecting and performing a captivating piece that showcases your talent."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-promotional-model", "id": 806, "slug": "how-to-become-a-promotional-model", "title": "How to Become a Promotional Model?", "time": "2023-05-26T06:45:16-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-promotional-model/comments", "total": 0}, "summary": "Explore the world of promotional modeling, where models help brands advertise by engaging with audiences at events. Discover the requirements, steps to become one, and tips for success. Learn about different job types and potential earnings, and find out where to access casting calls.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4126_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4130_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4130_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4130_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4130_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4130_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Explore the world of promotional modeling, where models help brands advertise by engaging with audiences at events. Discover the requirements, steps to become one, and tips for success. Learn about different job types and potential earnings, and find out where to access casting calls."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-marvel", "id": 804, "slug": "how-to-audition-for-marvel", "title": "How to Audition for Marvel?", "time": "2023-05-19T05:30:56-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-marvel/comments", "total": 0}, "summary": "Our exclusive guide can help you secure your spot in the Marvel Universe.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4114_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4118_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4118_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4118_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4118_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4118_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Our exclusive guide can help you secure your spot in the Marvel Universe."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-do-casting-directors-choose-actors", "id": 802, "slug": "how-do-casting-directors-choose-actors", "title": "How Do Casting Directors Choose Actors?", "time": "2023-05-09T02:59:51-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-do-casting-directors-choose-actors/comments", "total": 0}, "summary": "Casting directors choose actors based on role type, character appearance, and acting experience. They seek actors who fit the character's description and possess the necessary skills and personality traits. Headshots, experience, and suitability for the role are critical in the selection process.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4112_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4110_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4110_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4110_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4110_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4110_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Casting directors choose actors based on role type, character appearance, and acting experience. They seek actors who fit the character's description and possess the necessary skills and personality traits. Headshots, experience, and suitability for the role are critical in the selection process."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-on-a-dating-show", "id": 816, "slug": "how-to-get-on-a-dating-show", "title": "How to Get on a Dating Show?", "time": "2023-05-07T04:42:11-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-on-a-dating-show/comments", "total": 0}, "summary": "Explore the exciting world of reality TV dating shows ➡️.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4166_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4170_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4170_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4170_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4170_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4170_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Explore the exciting world of reality TV dating shows ➡️."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-hair-model", "id": 800, "slug": "how-to-become-a-hair-model", "title": "How to Become a Hair Model?", "time": "2023-04-24T06:10:05-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-hair-model/comments", "total": 0}, "summary": "Discover the steps to becoming a hair model, from casting calls to styling secrets.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4100_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4104_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4104_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4104_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4104_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4104_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Discover the steps to becoming a hair model, from casting calls to styling secrets."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/ghostbusters-afterlife-2-casting-call", "id": 798, "slug": "ghostbusters-afterlife-2-casting-call", "title": "Ghostbusters: Afterlife 2 Casting Call — How to get the role?", "time": "2023-04-17T02:27:02-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/ghostbusters-afterlife-2-casting-call/comments", "total": 0}, "summary": "\"Ghostbusters: Afterlife 2,\" co-written by <PERSON> and directed by <PERSON>, is set for release on December 20, 2023. The film features returning stars like <PERSON> and newcomers such as <PERSON><PERSON>. It follows a family discovering their connection to the original Ghostbusters.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4090_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4094_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4094_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4094_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4094_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4094_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "\"Ghostbusters: Afterlife 2,\" co-written by <PERSON> and directed by <PERSON>, is set for release on December 20, 2023. The film features returning stars like <PERSON> and newcomers such as <PERSON><PERSON>. It follows a family discovering their connection to the original Ghostbusters."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-into-modeling-with-no-experience", "id": 796, "slug": "how-to-get-into-modeling-with-no-experience", "title": "How to Get Into Modeling With NO Experience?", "time": "2023-04-13T23:23:59-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-into-modeling-with-no-experience/comments", "total": 0}, "summary": "<PERSON><PERSON>, Director of MMG New York, shares insights on starting a modeling career, emphasizing the importance of a strong Comp Card, professional social media presence, and a comprehensive portfolio. She offers advice on navigating different modeling types and preparing for casting calls.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4082_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4086_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4086_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4086_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4086_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4086_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "<PERSON><PERSON>, Director of MMG New York, shares insights on starting a modeling career, emphasizing the importance of a strong Comp Card, professional social media presence, and a comprehensive portfolio. She offers advice on navigating different modeling types and preparing for casting calls."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-do-actors-memorize-lines", "id": 788, "slug": "how-do-actors-memorize-lines", "title": "How Do Actors Memorize Lines?", "time": "2023-04-11T16:25:42-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-do-actors-memorize-lines/comments", "total": 0}, "summary": "Actors face the challenging task of memorizing lines, with the time required varying based on individual capacity and scene type. Techniques like repetition, using flashcards, and practicing with partners are common. Consistent practice and finding a personal method are key to success.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4042_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4046_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4046_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4046_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4046_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4046_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Actors face the challenging task of memorizing lines, with the time required varying based on individual capacity and scene type. Techniques like repetition, using flashcards, and practicing with partners are common. Consistent practice and finding a personal method are key to success."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-do-producers-look-for-when-casting-reality-shows", "id": 790, "slug": "what-do-producers-look-for-when-casting-reality-shows", "title": "What Do Producers Look for When Casting Reality Shows?", "time": "2023-04-11T16:20:20-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-do-producers-look-for-when-casting-reality-shows/comments", "total": 0}, "summary": "Discover the criteria, quirks, and key factors behind reality TV casting ➡️.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4056_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4060_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4060_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4060_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4060_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4060_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Discover the criteria, quirks, and key factors behind reality TV casting ➡️."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-create-a-modeling-portfolio", "id": 792, "slug": "how-to-create-a-modeling-portfolio", "title": "How to Create a Modeling Portfolio?", "time": "2023-04-11T16:18:17-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-create-a-modeling-portfolio/comments", "total": 0}, "summary": "Crafting a stunning modeling portfolio: Learn the steps to showcase your talent and versatility.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4064_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4068_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4068_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4068_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4068_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4068_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Crafting a stunning modeling portfolio: Learn the steps to showcase your talent and versatility."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-senior-model", "id": 794, "slug": "how-to-become-a-senior-model", "title": "How to Become a Senior Model?", "time": "2023-04-11T16:15:49-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-senior-model/comments", "total": 0}, "summary": "Age is just a number in the modeling world! Discover the steps to become a senior model.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4072_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4076_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4076_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4076_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4076_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4076_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Age is just a number in the modeling world! Discover the steps to become a senior model."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-call-godzilla-and-kong-2", "id": 786, "slug": "casting-call-godzilla-and-kong-2", "title": "Casting Call Godzilla And Kong 2024 — How To Get The Role?", "time": "2023-04-11T16:05:58-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-call-godzilla-and-kong-2/comments", "total": 0}, "summary": "Godzilla and Kong 2, directed by <PERSON>, is set to release on March 15, 2024. This sequel continues the MonsterVerse saga, featuring stars like <PERSON> and <PERSON>. Rumors suggest <PERSON><PERSON>od<PERSON> as the new villain, with <PERSON><PERSON> and <PERSON> teaming up once more.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4024_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4028_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4028_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4028_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4028_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4028_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Godzilla and Kong 2, directed by <PERSON>, is set to release on March 15, 2024. This sequel continues the MonsterVerse saga, featuring stars like <PERSON> and <PERSON>. Rumors suggest <PERSON><PERSON>od<PERSON> as the new villain, with <PERSON><PERSON> and <PERSON> teaming up once more."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-model-for-zara", "id": 784, "slug": "how-to-model-for-zara", "title": "How to Model for <PERSON><PERSON>?", "time": "2023-04-11T16:01:38-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-model-for-zara/comments", "total": 0}, "summary": "Zara, a leading fashion brand, offers aspiring models opportunities to join its diverse team. To become a Zara model, candidates should meet specific requirements such as height and a natural look. The process involves creating a portfolio, attending castings, and applying online.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4016_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4022_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4022_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4022_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4022_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4022_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Zara, a leading fashion brand, offers aspiring models opportunities to join its diverse team. To become a Zara model, candidates should meet specific requirements such as height and a natural look. The process involves creating a portfolio, attending castings, and applying online."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/become-a-brand-ambassador-today", "id": 698, "slug": "become-a-brand-ambassador-today", "title": "How to Become a Brand Ambassador?", "time": "2023-04-11T16:00:29-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=media", "id": 4, "title": "Media", "name": "media"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/become-a-brand-ambassador-today/comments", "total": 0}, "summary": "This is your chance to become a BRAND AMBASSADOR with 0 experience in the industry. All you need is dedication and some networking skills to get in.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4034_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4038_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4038_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4038_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4038_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4038_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "This is your chance to become a BRAND AMBASSADOR with 0 experience in the industry. All you need is dedication and some networking skills to get in."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/best-acting-techniques", "id": 782, "slug": "best-acting-techniques", "title": "Best Acting Techniques", "time": "2023-03-17T04:43:12-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/best-acting-techniques/comments", "total": 0}, "summary": "Discover the most popular acting techniques used by renowned actors to deliver compelling performances. Techniques like <PERSON><PERSON><PERSON>, <PERSON> Acting, and <PERSON><PERSON> focus on emotional authenticity and improvisation, while others like the <PERSON><PERSON><PERSON> and <PERSON> methods emphasize imagination and discipline.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4004_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4008_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4008_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4008_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4008_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4008_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Discover the most popular acting techniques used by renowned actors to deliver compelling performances. Techniques like <PERSON><PERSON><PERSON>, <PERSON> Acting, and <PERSON><PERSON> focus on emotional authenticity and improvisation, while others like the <PERSON><PERSON><PERSON> and <PERSON> methods emphasize imagination and discipline."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-nike-model", "id": 780, "slug": "how-to-become-a-nike-model", "title": "How to Become a Nike Model?", "time": "2023-03-17T04:07:19-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-nike-model/comments", "total": 0}, "summary": "Step into the world of sportswear modeling today.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3994_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3998_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3998_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3998_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3998_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3998_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Step into the world of sportswear modeling today."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-model-for-target", "id": 778, "slug": "how-to-model-for-target", "title": "How to Model for Target?", "time": "2023-03-17T03:07:23-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-model-for-target/comments", "total": 0}, "summary": "Explore the steps to become a model for Target and grace their campaigns with style.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3982_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3986_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3986_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3986_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3986_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3986_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "Explore the steps to become a model for Target and grace their campaigns with style."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-into-acting-at-30", "id": 776, "slug": "how-to-get-into-acting-at-30", "title": "How to Get Into Acting at 30?", "time": "2023-03-17T02:15:52-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-into-acting-at-30/comments", "total": 0}, "summary": "Never too late for the spotlight! Explore the path to break into acting at 30.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3972_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3976_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3976_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3976_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3976_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3976_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Never too late for the spotlight! Explore the path to break into acting at 30."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/top-self-tape-audition-tips", "id": 772, "slug": "top-self-tape-audition-tips", "title": "The Best Self-Tape Audition Tips", "time": "2023-02-07T06:14:55-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/top-self-tape-audition-tips/comments", "total": 0}, "summary": "<PERSON>, a seasoned Casting Director, shares essential tips for recording effective self-tapes and leaving lasting impressions during auditions. Her advice covers preparation, tech setup, and post-audition strategies, aiming to enhance actors' chances of success in the competitive entertainment industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3958_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4106_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4106_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4106_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4106_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4106_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "<PERSON>, a seasoned Casting Director, shares essential tips for recording effective self-tapes and leaving lasting impressions during auditions. Her advice covers preparation, tech setup, and post-audition strategies, aiming to enhance actors' chances of success in the competitive entertainment industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-wedding-model", "id": 768, "slug": "how-to-become-a-wedding-model", "title": "How to Become a Wedding Model?", "time": "2023-02-01T06:17:28-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-wedding-model/comments", "total": 0}, "summary": "Discover how to become a sought-after wedding model from scratch.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3940_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3944_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3944_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3944_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3944_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3944_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Discover how to become a sought-after wedding model from scratch."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-do-a-zoom-audition", "id": 766, "slug": "how-to-do-a-zoom-audition", "title": "How to Do a Zoom Audition?", "time": "2023-02-01T04:14:49-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-do-a-zoom-audition/comments", "total": 0}, "summary": "How to Do a Zoom Audition", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3930_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3934_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3934_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3934_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3934_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3934_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "How to Do a Zoom Audition"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-take-model-digitals", "id": 764, "slug": "how-to-take-model-digitals", "title": "How to Take Model Digitals?", "time": "2023-01-26T00:03:08-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-take-model-digitals/comments", "total": 0}, "summary": "How to Take Model Digitals", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3912_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3916_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3916_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3916_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3916_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3916_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "How to Take Model Digitals"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-face-model-for-maybe<PERSON>e", "id": 760, "slug": "how-to-become-a-face-model-for-maybelline", "title": "How to become a face model for <PERSON><PERSON><PERSON>?", "time": "2023-01-24T01:16:07-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-face-model-for-maybelline/comments", "total": 0}, "summary": "Aspiring face models for <PERSON><PERSON><PERSON> can boost their careers by meeting specific requirements like maintaining clear skin, healthy hair, and a fit physique. Confidence and a glowing complexion are essential. Pay varies based on model popularity, skills, and campaign visibility.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3880_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3884_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3884_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3884_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3884_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3884_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Aspiring face models for <PERSON><PERSON><PERSON> can boost their careers by meeting specific requirements like maintaining clear skin, healthy hair, and a fit physique. Confidence and a glowing complexion are essential. Pay varies based on model popularity, skills, and campaign visibility."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/should-i-be-an-actor", "id": 758, "slug": "should-i-be-an-actor", "title": "Quiz: Should I Be An Actor?", "time": "2023-01-10T05:08:34-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/should-i-be-an-actor/comments", "total": 0}, "summary": "Quiz Questions \"Should I Be an Actor?\"", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3872_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3876_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3876_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3876_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3876_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3876_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Quiz Questions \"Should I Be an Actor?\""}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-swimsuit-model", "id": 754, "slug": "how-to-become-a-swimsuit-model", "title": "How to Become a Swimsuit Model?", "time": "2022-12-30T04:55:22-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-swimsuit-model/comments", "total": 0}, "summary": "Altering poses, facial expressions, and gestures to express the designer and photographer's vision for any new swimsuit style are some of what it takes to become a swimsuit model.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3862_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3866_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3866_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3866_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3866_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3866_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Altering poses, facial expressions, and gestures to express the designer and photographer's vision for any new swimsuit style are some of what it takes to become a swimsuit model."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-to-wear-to-a-model-casting", "id": 752, "slug": "what-to-wear-to-a-model-casting", "title": "What to Wear to a Model Casting?", "time": "2022-12-30T04:08:50-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-to-wear-to-a-model-casting/comments", "total": 0}, "summary": "Discover what to wear to impress casting directors and stand out at auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3852_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3856_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3856_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3856_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3856_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3856_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Discover what to wear to impress casting directors and stand out at auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-be-an-hm-model", "id": 750, "slug": "how-to-be-an-hm-model", "title": "How to Be an H&M Model?", "time": "2022-12-30T03:13:49-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-be-an-hm-model/comments", "total": 0}, "summary": "Uncover the steps to become an H&M model, from building your portfolio to finding auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3846_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3850_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3850_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3850_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3850_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3850_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Uncover the steps to become an H&M model, from building your portfolio to finding auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-casting-director", "id": 748, "slug": "how-to-become-a-casting-director", "title": "How to Become a Casting Director?", "time": "2022-12-29T00:48:28-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-casting-director/comments", "total": 0}, "summary": "The film and entertainment industry knows way too well the importance of casting directors. But, if you're just finding out, and think this may be a great career choice for you: sit back, grab a cup of coffee (or tea, if you will), and read this article on how to kickstart your journey as a casting director.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3830_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3834_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3834_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3834_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3834_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3834_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "The film and entertainment industry knows way too well the importance of casting directors. But, if you're just finding out, and think this may be a great career choice for you: sit back, grab a cup of coffee (or tea, if you will), and read this article on how to kickstart your journey as a casting director."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-content-creator", "id": 746, "slug": "how-to-become-a-content-creator", "title": "How to Become a Content Creator?", "time": "2022-12-05T06:09:29-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-content-creator/comments", "total": 0}, "summary": "Every platform has one. Content creators are the reason every social media platform functions.\r\nWith some earning as much as millions every year, if approached correctly, print modeling can be a very profitable career.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3824_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3828_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3828_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3828_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3828_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3828_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Every platform has one. Content creators are the reason every social media platform functions.\r\nWith some earning as much as millions every year, if approached correctly, print modeling can be a very profitable career."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-foot-model", "id": 744, "slug": "how-to-become-a-foot-model", "title": "How to Become a Foot Model?", "time": "2022-11-29T00:17:01-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-foot-model/comments", "total": 0}, "summary": "Step into the world of foot modeling! Discover the path to showcasing your feet in fashion.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3808_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3812_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3812_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3812_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3812_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3812_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Step into the world of foot modeling! Discover the path to showcasing your feet in fashion."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-to-do-after-an-audition", "id": 742, "slug": "what-to-do-after-an-audition", "title": "What to Do After an Audition?", "time": "2022-11-28T07:46:39-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-to-do-after-an-audition/comments", "total": 0}, "summary": "If you think you’re a nervous wreck during your audition, just wait till after.\r\nWaiting for feedback after your audition can make you go bonkers for the following reasons...", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3794_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3798_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3798_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3798_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3798_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3798_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "If you think you’re a nervous wreck during your audition, just wait till after.\r\nWaiting for feedback after your audition can make you go bonkers for the following reasons..."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-hand-model", "id": 740, "slug": "how-to-become-hand-model", "title": "How to Become a Hand Model?", "time": "2022-11-28T07:10:02-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-hand-model/comments", "total": 0}, "summary": "Remember when your friend said, “you have such pretty hands”. \r\nWe are here to tell you that you can model your hands! \r\nSounds crazy?", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3784_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3788_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3788_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3788_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3788_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3788_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Remember when your friend said, “you have such pretty hands”. \r\nWe are here to tell you that you can model your hands! \r\nSounds crazy?"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-print-model", "id": 738, "slug": "how-to-become-a-print-model", "title": "How to Become a Print Model?", "time": "2022-11-25T06:46:10-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-print-model/comments", "total": 0}, "summary": "How many compliments did you receive this week telling you how good you look? \r\n\r\nIf it's anything more than zero, you should consider a career in print modeling!\r\n\r\nPrint modeling, unlike runway or fashion modeling, does not need you to look like a superhuman with top-tier Chris <PERSON>-level attractiveness.\r\n\r\nYou just have to look normal — in a good way!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3778_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3776_thumbnail_size1.jpg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3776_thumbnail_size2.jpg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3776_thumbnail_size3.jpg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3776_thumbnail_size4.jpg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3776_thumbnail_size5.jpg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "How many compliments did you receive this week telling you how good you look? \r\n\r\nIf it's anything more than zero, you should consider a career in print modeling!\r\n\r\nPrint modeling, unlike runway or fashion modeling, does not need you to look like a superhuman with top-tier Chris <PERSON>-level attractiveness.\r\n\r\nYou just have to look normal — in a good way!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/what-to-wear-for-commercial-audition", "id": 736, "slug": "what-to-wear-for-commercial-audition", "title": "What to Wear for Commercial Audition?", "time": "2022-10-26T12:23:31-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/what-to-wear-for-commercial-audition/comments", "total": 0}, "summary": "Equally as important as your performance itself, what you choose to wear can play a huge role in the success of your audition.\r\n\r\nIt is just as with every commercial; the character has a costume that reflects their role. Likewise, you have to wear something that reflects your role — a person applying for a commercial.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3756_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3752_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3752_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3752_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3752_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3752_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "Equally as important as your performance itself, what you choose to wear can play a huge role in the success of your audition.\r\n\r\nIt is just as with every commercial; the character has a costume that reflects their role. Likewise, you have to wear something that reflects your role — a person applying for a commercial."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-introduce-yourself-in-an-acting-audition", "id": 734, "slug": "how-to-introduce-yourself-in-an-acting-audition", "title": "How to Introduce <PERSON> in an Acting Audition?", "time": "2022-10-26T12:03:54-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-introduce-yourself-in-an-acting-audition/comments", "total": 0}, "summary": "The biggest stars you know and love today all started from somewhere. Most times, their big breaks happen because of how well they performed in their auditions.\r\nFor this reason, you have to look at your acting audition as the role that can make or break your entire career.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3746_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3742_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3742_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3742_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3742_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3742_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 20, "name": "<PERSON>"}, "intro": "The biggest stars you know and love today all started from somewhere. Most times, their big breaks happen because of how well they performed in their auditions.\r\nFor this reason, you have to look at your acting audition as the role that can make or break your entire career."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-commercials", "id": 730, "slug": "how-to-audition-for-commercials", "title": "How To Audition For Commercials?", "time": "2022-09-30T05:44:30-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-audition-for-commercials/comments", "total": 0}, "summary": "Learn the ropes, tips, and strategies to shine in the world of TV and print advertising.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3704_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3700_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3700_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3700_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3700_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3700_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "Learn the ropes, tips, and strategies to shine in the world of TV and print advertising."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-fashion-content-creator", "id": 728, "slug": "how-to-become-a-fashion-content-creator", "title": "How to Become a Fashion Content Creator?", "time": "2022-09-30T04:28:28-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-fashion-content-creator/comments", "total": 0}, "summary": "Aspiring fashion content creators can learn essential skills, explore potential earnings, and discover how to break into the industry. With creativity, adaptability, and consistency, you can build a successful career in fashion content creation and even apply for casting calls.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3694_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3690_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3690_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3690_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3690_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3690_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 8, "name": "<PERSON>"}, "intro": "Aspiring fashion content creators can learn essential skills, explore potential earnings, and discover how to break into the industry. With creativity, adaptability, and consistency, you can build a successful career in fashion content creation and even apply for casting calls."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-background-actor", "id": 724, "slug": "how-to-become-a-background-actor", "title": "How To Become A Background Actor?", "time": "2022-09-30T03:06:13-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-background-actor/comments", "total": 0}, "summary": "As an aspiring actor without much experience, if any at all, you are checking the casting calls and weighing various options about what to apply to. But becoming a background actor might be your best bet right now. Read on to learn exactly why!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3678_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3674_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3674_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3674_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3674_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3674_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "As an aspiring actor without much experience, if any at all, you are checking the casting calls and weighing various options about what to apply to. But becoming a background actor might be your best bet right now. Read on to learn exactly why!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/8-myths-about-casting-platforms", "id": 722, "slug": "8-myths-about-casting-platforms", "title": "8 Myths About Casting Platforms", "time": "2022-08-24T05:26:16-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/8-myths-about-casting-platforms/comments", "total": 0}, "summary": "The World Wide Web's influence on every single aspect of our lives and industries has been undeniable. Even on casting! But, as it tends to happen with new features to decades' proven approaches, casting platforms sometimes happen to get viewed a bit ambiguously. Is there any truth to the myths?", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3668_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3650_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3650_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3650_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3650_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3650_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "The World Wide Web's influence on every single aspect of our lives and industries has been undeniable. Even on casting! But, as it tends to happen with new features to decades' proven approaches, casting platforms sometimes happen to get viewed a bit ambiguously. Is there any truth to the myths?"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-find-content-creator", "id": 720, "slug": "how-to-find-content-creator", "title": "How To Find Content Creators?", "time": "2022-08-24T04:37:15-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-find-content-creator/comments", "total": 0}, "summary": "Content is gold. If you have been keeping up with the trends within the entertainment industry in the last few years, you already know how important content is to consumers' decisions regarding a brand. Here’s what to know about finding the right person who'll create it for you.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3664_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3660_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3660_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3660_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3660_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3660_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Content is gold. If you have been keeping up with the trends within the entertainment industry in the last few years, you already know how important content is to consumers' decisions regarding a brand. Here’s what to know about finding the right person who'll create it for you."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/doactorskiss", "id": 718, "slug": "doactorskiss", "title": "Do Actors Really Kiss?", "time": "2022-08-03T06:56:47-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=media", "id": 4, "title": "Media", "name": "media"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/doactorskiss/comments", "total": 0}, "summary": "So, do actors really kiss? Short answer: read our article and find out!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3630_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3628_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3628_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3628_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3628_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3628_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "So, do actors really kiss? Short answer: read our article and find out!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-plus-size-model-with-no-experience", "id": 716, "slug": "how-to-become-a-plus-size-model-with-no-experience", "title": "How to Become a Plus-Size Model with No Experience?", "time": "2022-07-25T06:49:27-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-plus-size-model-with-no-experience/comments", "total": 0}, "summary": "The pursuit of art, which also includes posing for photo projects, does not depend on the physique. Nowadays any body type can succeed in the modeling industry. Recently more and more brands are casting plus-size models to manifest diversity and body positivity! How to be a successful plus size model and what it takes to launch your career? Start taking active steps in the right direction, and soon enough you might pose for one of the high fashion houses.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3612_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3608_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3608_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3608_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3608_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3608_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "The pursuit of art, which also includes posing for photo projects, does not depend on the physique. Nowadays any body type can succeed in the modeling industry. Recently more and more brands are casting plus-size models to manifest diversity and body positivity! How to be a successful plus size model and what it takes to launch your career? Start taking active steps in the right direction, and soon enough you might pose for one of the high fashion houses."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-commercial-model", "id": 714, "slug": "how-to-become-a-commercial-model", "title": "How to Become a Commercial Model?", "time": "2022-07-25T06:34:37-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-become-a-commercial-model/comments", "total": 0}, "summary": "Learn how to break into the industry as a commercial model.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3602_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3598_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3598_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3598_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3598_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3598_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Learn how to break into the industry as a commercial model."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-be-a-model-for-clothing-brands", "id": 712, "slug": "how-to-be-a-model-for-clothing-brands", "title": "How to Be a Model for Clothing Brands?", "time": "2022-07-25T06:02:46-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-be-a-model-for-clothing-brands/comments", "total": 0}, "summary": "Where does the road to the modeling business begin? At first, a timid dream is born, but if this is something to which you are ready to devote your strength and energy, it becomes more real every day. And one day, it brings you to a point where you need to make practical steps to make it happen.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3596_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3594_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3594_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3594_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3594_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3594_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Where does the road to the modeling business begin? At first, a timid dream is born, but if this is something to which you are ready to devote your strength and energy, it becomes more real every day. And one day, it brings you to a point where you need to make practical steps to make it happen."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/actorwithnoexperience", "id": 704, "slug": "actorwithnoexperience", "title": "How to Become an Actor With No Experience?", "time": "2022-06-22T02:24:33-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=media", "id": 4, "title": "Media", "name": "media"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/actorwithnoexperience/comments", "total": 0}, "summary": "You're wondering how to start acting since it's something you always dreamed of trying, but some things stop you, right? Things like \"I don't have the required education, experience, it's too late to start now, etc.\"\r\n\r\n But we all have to start somewhere, so why not do it now.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3520_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3518_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3518_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3518_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3518_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3518_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "You're wondering how to start acting since it's something you always dreamed of trying, but some things stop you, right? Things like \"I don't have the required education, experience, it's too late to start now, etc.\"\r\n\r\n But we all have to start somewhere, so why not do it now."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/director-spotlight-nachela-knox-ceo-gogetherproductions", "id": 692, "slug": "director-spotlight-nachela-knox-ceo-gogetherproductions", "title": "Director Spotlight: <PERSON><PERSON><PERSON>, Director and CEO of GoGetHer Productions", "time": "2021-12-20T12:31:45-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/director-spotlight-nachela-knox-ceo-gogetherproductions/comments", "total": 0}, "summary": "In this interview, we explore how <PERSON><PERSON><PERSON>, Director, Actress and CEO of GoGetHer Productions casts for her upcoming projects, as well as share some tips on what aspiring actors need to do to increase their chances of an audition.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3456_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3452_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3452_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3452_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3452_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3452_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, we explore how <PERSON><PERSON><PERSON>, Director, Actress and CEO of GoGetHer Productions casts for her upcoming projects, as well as share some tips on what aspiring actors need to do to increase their chances of an audition."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-caroly<PERSON>-jen<PERSON>-casting-producer", "id": 686, "slug": "casting-spotlight-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-casting-producer", "title": "Casting Spotlight: <PERSON>, Casting Director/Casting Producer", "time": "2021-11-21T23:59:53-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-caroly<PERSON>-jen<PERSON>-casting-producer/comments", "total": 0}, "summary": "In this interview, we explore what talent needs in order to get cast by experienced Casting Director and Casting Producer <PERSON>.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3436_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3432_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3432_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3432_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3432_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3432_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, we explore what talent needs in order to get cast by experienced Casting Director and Casting Producer <PERSON>."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/director-spotlight-brad-leo-lyon", "id": 684, "slug": "director-spotlight-brad-leo-lyon", "title": "Director Spotlight: <PERSON>, Director/Producer of Lyon Productions", "time": "2021-11-17T01:59:53-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/director-spotlight-brad-leo-lyon/comments", "total": 0}, "summary": "In this interview, we sit down with <PERSON>, Director/Producer of Lyon Productions to discuss his multifaceted journey through the world of filmmaking, as well as explore the ways talent can increase their chances of getting cast.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3416_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3412_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3412_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3412_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3412_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3412_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, we sit down with <PERSON>, Director/Producer of Lyon Productions to discuss his multifaceted journey through the world of filmmaking, as well as explore the ways talent can increase their chances of getting cast."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-sabri<PERSON>-sa<PERSON><PERSON>-director-of-casting-hearst", "id": 682, "slug": "casting-spotlight-sa<PERSON><PERSON>-sa<PERSON><PERSON>-director-of-casting-hearst", "title": "Casting Spotlight: <PERSON>, Director of Casting at Hearst Magazines", "time": "2021-11-15T07:42:16-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-sabri<PERSON>-sa<PERSON><PERSON>-director-of-casting-hearst/comments", "total": 0}, "summary": "In this interview, <PERSON>, Director of Casting at Hearst Magazines (Cosmopolitan, Elle, Esquire, etc) shares some insight into her journey to becoming Director of Casting and Talent Development at Hearst as well as some tips for aspiring models and actors in the industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3408_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3404_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3404_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3404_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3404_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3404_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, <PERSON>, Director of Casting at Hearst Magazines (Cosmopolitan, Elle, Esquire, etc) shares some insight into her journey to becoming Director of Casting and Talent Development at Hearst as well as some tips for aspiring models and actors in the industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-rachel-chaves-casting-director", "id": 680, "slug": "casting-spotlight-rachel-chaves-casting-director", "title": "Casting Spotlight: <PERSON>, Casting Director of rachelchaves.com", "time": "2021-11-15T00:27:05-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-rachel-chaves-casting-director/comments", "total": 0}, "summary": "In this interview, we sit down with <PERSON>, Casting Director of rachelchaves.com, and talk about her journey to becoming a successful Casting Professional in the Toronto area.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3400_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3396_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3396_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3396_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3396_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3396_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, we sit down with <PERSON>, Casting Director of rachelchaves.com, and talk about her journey to becoming a successful Casting Professional in the Toronto area."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-k-c-roberts-1starr", "id": 676, "slug": "casting-spotlight-k-c-roberts-1st<PERSON>r", "title": "Casting Spotlight: <PERSON><PERSON><PERSON>, Casting Director for 1Starr Enterprises", "time": "2021-11-05T00:56:24-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-k-c-roberts-1starr/comments", "total": 0}, "summary": "In this interview, <PERSON><PERSON><PERSON>, Casting Director and Actress for 1Starr Enterprises, tells us about her journey to becoming a successful Casting Director, and shares some crucial insight into working in the entertainment industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3428_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3424_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3424_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3424_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3424_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3424_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, <PERSON><PERSON><PERSON>, Casting Director and Actress for 1Starr Enterprises, tells us about her journey to becoming a successful Casting Director, and shares some crucial insight into working in the entertainment industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-elle-jones", "id": 674, "slug": "casting-spotlight-elle-jones", "title": "Casting Spotlight: <PERSON>, Casting Director and CEO of <PERSON> Casting", "time": "2021-11-01T01:58:55-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-elle-jones/comments", "total": 0}, "summary": "In this interview, <PERSON>, Casting Director and CEO of <PERSON> Jones Casting, tells us about her journey to becoming a successful Casting Director, and shares some crucial insight into working with talent with special needs and disabilities.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3342_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3338_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3338_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3338_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3338_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3338_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, <PERSON>, Casting Director and CEO of <PERSON> Jones Casting, tells us about her journey to becoming a successful Casting Director, and shares some crucial insight into working with talent with special needs and disabilities."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-lindsay-christopher-casting-director", "id": 664, "slug": "casting-spotlight-lindsay-christopher-casting-director", "title": "Casting Spotlight: <PERSON>, Casting Director at castingmaster.ca", "time": "2021-10-27T02:05:30-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-lindsay-christopher-casting-director/comments", "total": 0}, "summary": "In this interview, <PERSON>, Casting Director of castingmaster.ca tells us about her journey to becoming a successful Casting Director, and shares some important tips for talent seeking to nail their auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3308_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3304_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3304_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3304_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3304_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3304_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, <PERSON>, Casting Director of castingmaster.ca tells us about her journey to becoming a successful Casting Director, and shares some important tips for talent seeking to nail their auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-karlie-<PERSON><PERSON>-casting-director", "id": 662, "slug": "casting-spotlight-karlie-<PERSON><PERSON>-casting-director", "title": "Casting Spotlight: <PERSON><PERSON>, Casting Director for KLR Creative Group", "time": "2021-10-26T00:33:55-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-karlie-<PERSON><PERSON>-casting-director/comments", "total": 0}, "summary": "In this interview, <PERSON><PERSON>, Casting Director for KLR Creative Group tells us about her journey to becoming a successful Casting Director, and shares some important tips for talent seeking to nail their auditions.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3300_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3296_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3296_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3296_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3296_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3296_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "In this interview, <PERSON><PERSON>, Casting Director for KLR Creative Group tells us about her journey to becoming a successful Casting Director, and shares some important tips for talent seeking to nail their auditions."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/producer-spotlight-melvin-cotton", "id": 660, "slug": "producer-spotlight-melvin-cotton", "title": "Producer Spotlight: <PERSON>, Creative Producer", "time": "2021-10-22T00:07:01-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/producer-spotlight-melvin-cotton/comments", "total": 0}, "summary": "<PERSON>, Creative Producer shares some insight on casting the right talent for his projects.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3292_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3288_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3288_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3288_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3288_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3288_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "<PERSON>, Creative Producer shares some insight on casting the right talent for his projects."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-bobby-silva", "id": 658, "slug": "casting-spotlight-bobby-silva", "title": "Casting Spotlight: <PERSON>, Casting Producer/NBC", "time": "2021-10-20T23:22:00-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-bobby-silva/comments", "total": 0}, "summary": "<PERSON>, Casting Producer for NBC shares some important tips on getting into an audition", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3284_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3280_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3280_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3280_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3280_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3280_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "<PERSON>, Casting Producer for NBC shares some important tips on getting into an audition"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-jana-beeson-casting-director", "id": 654, "slug": "casting-spotlight-jana-<PERSON>on-casting-director", "title": "Casting Spotlight: <PERSON>, Casting Director and CEO of Act NOW!", "time": "2021-10-15T00:04:37-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-jana-beeson-casting-director/comments", "total": 0}, "summary": "Experienced Casting Director, Actress and CEO of ACT Now, <PERSON> shares some important insight and crucial tips on making it in the entertainment industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3268_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3264_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3264_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3264_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3264_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3264_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Experienced Casting Director, Actress and CEO of ACT Now, <PERSON> shares some important insight and crucial tips on making it in the entertainment industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/industry-spotlight-nicole-butler-casting-professional", "id": 652, "slug": "industry-spotlight-nicole-butler-casting-professional", "title": "Industry Spotlight: <PERSON>, Casting Professional/Producer", "time": "2021-10-11T12:04:11-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/industry-spotlight-nicole-butler-casting-professional/comments", "total": 0}, "summary": "Experienced casting professional <PERSON> shares some insights on casting children for a variety of productions and shares invaluable tips for parents of child talent.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3260_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3256_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3256_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3256_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3256_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3256_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Experienced casting professional <PERSON> shares some insights on casting children for a variety of productions and shares invaluable tips for parents of child talent."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/industry-spotlight-lana-montalban-talent-agent", "id": 650, "slug": "industry-spotlight-lana-mon<PERSON>ban-talent-agent", "title": "Industry Spotlight: <PERSON>, Talent Agent", "time": "2021-10-01T09:24:00-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/industry-spotlight-lana-montal<PERSON>-talent-agent/comments", "total": 0}, "summary": "Talent Agent and Representative <PERSON> shares some industry insights in this exclusive allcasting interview.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3242_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3238_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3238_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3238_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3238_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3238_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Talent Agent and Representative <PERSON> shares some industry insights in this exclusive allcasting interview."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/talent-spotlight-josh<PERSON>-be<PERSON><PERSON>-actor", "id": 646, "slug": "talent-spotlight-josh<PERSON><PERSON><PERSON><PERSON><PERSON>-actor", "title": "Talent Spotlight: <PERSON>, Actor/Producer", "time": "2021-09-28T02:04:51-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/talent-spotlight-josh<PERSON>-berg<PERSON>-actor/comments", "total": 0}, "summary": "Actor and Producer <PERSON> shares insights on his path towards acting and producing.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3220_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3216_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3216_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3216_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3216_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3216_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Actor and Producer <PERSON> shares insights on his path towards acting and producing."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-hank-isaac-producer-director", "id": 644, "slug": "casting-spotlight-hank-isaac-producer-director", "title": "Casting Spotlight: <PERSON>, Producer/Director", "time": "2021-09-27T02:20:00-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-hank-isaac-producer-director/comments", "total": 0}, "summary": "Independent Producer and Director <PERSON> shares some insights into casting child talent for feature films and shows.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3198_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3194_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3194_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3194_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3194_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3194_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Independent Producer and Director <PERSON> shares some insights into casting child talent for feature films and shows."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-jona<PERSON>-tanzman", "id": 636, "slug": "casting-spotlight-j<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Casting Spotlight: <PERSON>, Independent Casting Director", "time": "2021-09-10T04:36:06-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-jona<PERSON>-tanzman/comments", "total": 0}, "summary": "Independent Casting Director <PERSON> steps into our weekly Casting Spotlight to answer some of the most pressing and important questions both starting and experienced actors have in regards to the casting industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3104_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3100_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3100_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3100_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3100_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3100_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "Independent Casting Director <PERSON> steps into our weekly Casting Spotlight to answer some of the most pressing and important questions both starting and experienced actors have in regards to the casting industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-wendy-woods-head-of-scouting", "id": 634, "slug": "casting-spotlight-wendy-woods-head-of-scouting", "title": "Casting Spotlight: <PERSON>, Head of Scouting at Stars Talent Studio", "time": "2021-09-01T09:38:56-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/casting-spotlight-wendy-woods-head-of-scouting/comments", "total": 0}, "summary": "<PERSON> is a seasoned casting professional working at Stars Talent Studio in Los Angeles, California. In this interview, <PERSON> shares some important tips and recommendations for both adults and minors looking to enter the entertainment industry.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3146_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4194_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4194_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4194_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4194_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4194_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 16, "name": "<PERSON>"}, "intro": "<PERSON> is a seasoned casting professional working at Stars Talent Studio in Los Angeles, California. In this interview, <PERSON> shares some important tips and recommendations for both adults and minors looking to enter the entertainment industry."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/perfect-allcasting-profile-guide", "id": 592, "slug": "perfect-allcasting-profile-guide", "title": "Beginner's Guide: Create the PERFECT allcasting Profile", "time": "2020-06-03T03:00:50-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/perfect-allcasting-profile-guide/comments", "total": 0}, "summary": "In this guide, we take you through everything you need to know to create an allcasting profile that will get you cast.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/03/thumb_2738_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2734_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2734_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2734_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2734_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2734_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "In this guide, we take you through everything you need to know to create an allcasting profile that will get you cast."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/correct-way-to-apply-to-casting-calls", "id": 590, "slug": "correct-way-to-apply-to-casting-calls", "title": "The CORRECT WAY to Apply to Casting Calls", "time": "2020-05-25T00:01:38-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/correct-way-to-apply-to-casting-calls/comments", "total": 0}, "summary": "There's a correct way to apply to casting calls. Most aspiring talents DON’T do these things. \r\nYou should. Read more.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/03/thumb_2722_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2720_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2720_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2720_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2720_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/03/thumb_2720_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "There's a correct way to apply to casting calls. Most aspiring talents DON’T do these things. \r\nYou should. Read more."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/special-skills-for-actors-the-complete-guide", "id": 560, "slug": "special-skills-for-actors-the-complete-guide", "title": "Special Skills for Actors: The Complete Guide", "time": "2019-11-04T01:24:51-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/special-skills-for-actors-the-complete-guide/comments", "total": 0}, "summary": "Learn everything you need to know about special skills for actors with this complete guide. Tips, tricks & best practices with real examples. Read now.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/03/thumb_2468_story_pic_sizeStory.jpeg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3362_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3362_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3362_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3362_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/04/thumb_3362_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Learn everything you need to know about special skills for actors with this complete guide. Tips, tricks & best practices with real examples. Read now."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/the-importance-of-headshots", "id": 352, "slug": "the-importance-of-headshots", "title": "How to Take Good Headshot for Actors?", "time": "2018-08-28T08:09:10-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/the-importance-of-headshots/comments", "total": 0}, "summary": "Discover the secrets to a standout actor's headshot, from preparation to the photoshoot.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/02/thumb_1494_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/02/thumb_1494_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/02/thumb_1494_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/02/thumb_1494_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/02/thumb_1494_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Discover the secrets to a standout actor's headshot, from preparation to the photoshoot."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/types-of-modeling-jobs", "id": 234, "slug": "types-of-modeling-jobs", "title": "What Are The Most Popular Types of Modeling Jobs?", "time": "2018-03-19T05:15:48-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/types-of-modeling-jobs/comments", "total": 0}, "summary": "When most people think of modeling industry, they automatically think of supermodels <PERSON><PERSON> and <PERSON> or <PERSON>. However, there are many other types of models whose names you wouldn't know but who are profiting big time. Click to read more where you -- your wonderful and unique self -- would fit in the best!", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_970_thumbnail_size1.jpeg", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_970_thumbnail_size2.jpeg", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_970_thumbnail_size3.jpeg", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_970_thumbnail_size4.jpeg", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_970_thumbnail_size5.jpeg", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "When most people think of modeling industry, they automatically think of supermodels <PERSON><PERSON> and <PERSON> or <PERSON>. However, there are many other types of models whose names you wouldn't know but who are profiting big time. Click to read more where you -- your wonderful and unique self -- would fit in the best!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/pros-cons-talent-agent", "id": 162, "slug": "pros-cons-talent-agent", "title": "What are the Pros and Cons of Having a Talent Agent?", "time": "2017-12-05T05:43:43-08:00", "featured": 0, "size": 4, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/pros-cons-talent-agent/comments", "total": 0}, "summary": "There is one thing actors obsess over -- and it is getting an agent. They don’t know why they need one or how to get one, but everyone is pretty sure they're acting career is going to skyrocket once that box is filled in. Of course, an agent can come in pretty handy. They can undoubtedly eliminate a whole lot of paperwork and even open a few doors.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_542_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_542_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_542_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_542_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_542_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "There is one thing actors obsess over -- and it is getting an agent. They don’t know why they need one or how to get one, but everyone is pretty sure they're acting career is going to skyrocket once that box is filled in. Of course, an agent can come in pretty handy. They can undoubtedly eliminate a whole lot of paperwork and even open a few doors."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-avoid-casting-scams", "id": 110, "slug": "how-to-avoid-casting-scams", "title": "How To Avoid Casting <PERSON><PERSON>?", "time": "2017-10-06T06:45:42-07:00", "featured": 1, "size": 4, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "id": 4, "title": "Lessons", "name": "lessons", "slug": "lessons"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-avoid-casting-scams/comments", "total": 0}, "summary": "From time to time we hear about incidents in the entertainment community where \"casting directors\" try soliciting people, offering them not only paid acting jobs but also to cover travel and accommodation expenses, by paying significant amounts of money upfront, only to later discover that the check is fraudulent.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_292_thumbnail_size1.png", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_292_thumbnail_size2.png", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_292_thumbnail_size3.png", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_292_thumbnail_size4.png", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/01/thumb_292_thumbnail_size5.png", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "From time to time we hear about incidents in the entertainment community where \"casting directors\" try soliciting people, offering them not only paid acting jobs but also to cover travel and accommodation expenses, by paying significant amounts of money upfront, only to later discover that the check is fraudulent."}], "pagination": {"next": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=140&categories=lessons&start=110"}, "first": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=140&categories=lessons"}, "last": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=140&categories=lessons&end=0"}}, "filters": {"none": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles", "total": 156}, "news": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "total": 11}, "lessons": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "total": 139}, "info": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=info", "total": 6}, "audition-tips": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=audition-tips", "total": 0}, "success-stories": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=success-stories", "total": 0}, "skill-development": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=skill-development", "total": 0}, "career-paths": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=career-paths", "total": 0}, "industry-news": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=industry-news", "total": 0}, "beginner-s-guide": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=beginner-s-guide", "total": 0}, "casting-faqs": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=casting-faqs", "total": 0}, "awards": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=awards", "total": 0}}, "http_status": 200}