{"href": "https://api.allcasting.com/api/gta/articles?limit=12&categories=news", "status": "ok", "total": 11, "items": [{"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-voiceover-work", "id": 891, "slug": "how-to-get-voiceover-work", "title": "How to Get Voiceover Work?", "time": "2023-12-27T05:23:59-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-get-voiceover-work/comments", "total": 0}, "summary": "Breaking into the voiceover industry can be challenging, but multi-award-winning producer <PERSON> shares insights on how to start a successful career. He emphasizes the importance of auditioning, building skills gradually, and finding opportunities on platforms like Twitter and Facebook groups.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4472_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4472_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4472_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4472_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4472_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 10, "name": "<PERSON>"}, "intro": "Breaking into the voiceover industry can be challenging, but multi-award-winning producer <PERSON> shares insights on how to start a successful career. He emphasizes the importance of auditioning, building skills gradually, and finding opportunities on platforms like Twitter and Facebook groups."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/mastering-the-art-of-short-films", "id": 890, "slug": "mastering-the-art-of-short-films", "title": "Mastering The Art of Short Films — Interview with <PERSON>", "time": "2023-12-27T03:38:51-08:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/mastering-the-art-of-short-films/comments", "total": 0}, "summary": "<PERSON>, an award-winning filmmaker with over 30 years in the industry, co-founded iFilmGroup to mentor emerging talent. His films, known for tackling social issues, have won numerous awards. <PERSON> focuses on nurturing filmmakers through workshops and an upcoming online training platform.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4470_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4470_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4470_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4470_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4470_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "<PERSON>, an award-winning filmmaker with over 30 years in the industry, co-founded iFilmGroup to mentor emerging talent. His films, known for tackling social issues, have won numerous awards. <PERSON> focuses on nurturing filmmakers through workshops and an upcoming online training platform."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-cast-a-movie", "id": 879, "slug": "how-to-cast-a-movie", "title": "How To Cast a Movie?", "time": "2023-12-12T00:15:47-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-cast-a-movie/comments", "total": 0}, "summary": "Veteran producer <PERSON> discusses the complexities of casting, emphasizing the importance of factors like location, talent type, and personality. He offers insights on how actors can improve their chances and highlights the shift to online auditions post-COVID.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4450_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4450_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4450_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4450_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4450_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "Veteran producer <PERSON> discusses the complexities of casting, emphasizing the importance of factors like location, talent type, and personality. He offers insights on how actors can improve their chances and highlights the shift to online auditions post-COVID."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-find-a-perfect-talent", "id": 847, "slug": "how-to-find-a-perfect-talent", "title": "How To Find A Perfect Talent In Less Than A Day?", "time": "2023-08-22T02:29:04-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-find-a-perfect-talent/comments", "total": 0}, "summary": "Discover how Allcasting.com simplifies your talent search with expert Personal Managers. Effortlessly post projects, filter talent, and boost visibility through a vast database and social media. Perfect for finding actors, influencers, or performers. Request a demo to learn more.", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/05/thumb_4309_story_pic_sizeStory.jpg", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4476_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4476_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4476_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4476_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4476_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 4, "name": "<PERSON>"}, "intro": "Discover how Allcasting.com simplifies your talent search with expert Personal Managers. Effortlessly post projects, filter talent, and boost visibility through a vast database and social media. Perfect for finding actors, influencers, or performers. Request a demo to learn more."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/review-fady-zab<PERSON>k", "id": 774, "slug": "review-f<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "title": "Success Story: <PERSON><PERSON>", "time": "2023-02-23T01:51:31-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/review-fady-zab<PERSON><PERSON>/comments", "total": 0}, "summary": "<PERSON><PERSON>'s journey from war-torn Iraq to becoming a successful model and actor in Toronto is a testament to perseverance and passion. Despite numerous challenges, he pursued his dreams, proving that hard work and dedication can lead to remarkable achievements.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4478_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4478_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4478_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4478_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4478_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 6, "name": "<PERSON>"}, "intro": "<PERSON><PERSON>'s journey from war-torn Iraq to becoming a successful model and actor in Toronto is a testament to perseverance and passion. Despite numerous challenges, he pursued his dreams, proving that hard work and dedication can lead to remarkable achievements."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/how-to-invite-talent-to-a-casting-call", "id": 762, "slug": "how-to-invite-talent-to-a-casting-call", "title": "How to Invite <PERSON> to a Casting Call on allcasting.com?", "time": "2023-01-24T05:02:00-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/how-to-invite-talent-to-a-casting-call/comments", "total": 0}, "summary": "How to invite talent to a casting call", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4480_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4480_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4480_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4480_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4480_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "How to invite talent to a casting call"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/castingdirectors", "id": 726, "slug": "castingdirectors", "title": "Connecting Casting Directors with Talent", "time": "2022-09-30T03:26:37-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=media", "id": 4, "title": "Media", "name": "media"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/castingdirectors/comments", "total": 0}, "summary": "Are you a casting professional looking for top-rated actors or talent just starting their career in the entertainment industry? If so, then you are at the right place—the place where talent connects with industry professionals and vice versa.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4482_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4482_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4482_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4482_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4482_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Are you a casting professional looking for top-rated actors or talent just starting their career in the entertainment industry? If so, then you are at the right place—the place where talent connects with industry professionals and vice versa."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/lamesha-sanders-interview", "id": 710, "slug": "lamesha-sanders-interview", "title": "Success Story: <PERSON><PERSON><PERSON>", "time": "2022-07-11T01:09:50-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/lamesha-sanders-interview/comments", "total": 0}, "summary": "<PERSON><PERSON><PERSON> never had any acting experience before joining allcasting, yet she has received the part in The New Color Purple musical movie featuring such stars as R&B singer <PERSON><PERSON><PERSON><PERSON> \r\n Read her full interview below to find out more.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4484_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4484_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4484_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4484_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4484_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "<PERSON><PERSON><PERSON> never had any acting experience before joining allcasting, yet she has received the part in The New Color Purple musical movie featuring such stars as R&B singer <PERSON><PERSON><PERSON><PERSON> \r\n Read her full interview below to find out more."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/livvy-shaffery-success-story", "id": 708, "slug": "livvy-shaffery-success-story", "title": "Success Story: Rising Star Liv<PERSON> Shares", "time": "2022-07-07T05:39:49-07:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/livvy-shaffery-success-story/comments", "total": 0}, "summary": "<PERSON><PERSON> is an 18-year-old actress and model who has already participated in two major projects through allcasting. One of them is a lead role for an Amazon feature film! \r\n\r\n Read her interview below to find out how she did it!", "tldr_summary": null, "story-pic": {"sizeStory": {"rel": "image", "href": "https://static.allcasting.com/articles/story_pic/0001/04/thumb_3536_story_pic_sizeStory.png", "name": null}}, "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4486_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4486_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4486_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4486_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4486_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "<PERSON><PERSON> is an 18-year-old actress and model who has already participated in two major projects through allcasting. One of them is a lead role for an Amazon feature film! \r\n\r\n Read her interview below to find out how she did it!"}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/introducing-profile-ratings-get-cast", "id": 578, "slug": "introducing-profile-ratings-get-cast", "title": "What is a Profile Rating on allcasting?", "time": "2019-12-18T00:12:54-08:00", "featured": 0, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/introducing-profile-ratings-get-cast/comments", "total": 0}, "summary": "Profile ratings are here! This means that you've now got a better chance to get noticed by casting directors. Why & how does it work? Read here.", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4488_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4488_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4488_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4488_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4488_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 14, "name": "<PERSON>"}, "intro": "Profile ratings are here! This means that you've now got a better chance to get noticed by casting directors. Why & how does it work? Read here."}, {"rel": "article", "href": "https://api.allcasting.com/api/gta/articles/tyler-rivera-success-story", "id": 280, "slug": "tyler-rivera-success-story", "title": "Rising Star <PERSON> Shares His allcasting Success Story", "time": "2018-06-04T05:56:07-07:00", "featured": 1, "size": 1, "article_status": 1, "redirect_url": null, "skill_level": "<PERSON><PERSON><PERSON>", "type": {"rel": "type", "href": "https://api.allcasting.com/api/gta/articles?types=textual", "id": 2, "title": "Textual", "name": "textual"}, "category": {"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news"}, "categories": [{"rel": "category", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "id": 2, "title": "News", "name": "news", "slug": "news"}], "comments": {"rel": "comment-collection", "href": "https://api.allcasting.com/api/gta/articles/tyler-rivera-success-story/comments", "total": 0}, "summary": "<PERSON> is a 20-year-old actor living in New York. In this short, but heartfelt interview he tells us about his recent acting gigs, his struggles and how he got his first real acting jobs through AllCasting.com", "tldr_summary": null, "story-pic": [], "thumbnail-image": {"size1": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4474_thumbnail_size1.webp", "name": null}, "size2": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4474_thumbnail_size2.webp", "name": null}, "size3": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4474_thumbnail_size3.webp", "name": null}, "size4": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4474_thumbnail_size4.webp", "name": null}, "size5": {"rel": "image", "href": "https://static.allcasting.com/articles/thumbnail/0001/05/thumb_4474_thumbnail_size5.webp", "name": null}}, "questions_and_answers": [], "how_to": null, "author": {"id": 2, "name": "<PERSON>"}, "intro": "<PERSON> is a 20-year-old actor living in New York. In this short, but heartfelt interview he tells us about his recent acting gigs, his struggles and how he got his first real acting jobs through AllCasting.com"}], "pagination": {"next": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=12&categories=news&start=280"}, "prev": null, "first": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=12&categories=news"}, "last": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?limit=12&categories=news&end=0"}}, "filters": {"none": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles", "total": 156}, "news": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=news", "total": 11}, "lessons": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=lessons", "total": 139}, "info": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=info", "total": 6}, "audition-tips": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=audition-tips", "total": 0}, "success-stories": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=success-stories", "total": 0}, "skill-development": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=skill-development", "total": 0}, "career-paths": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=career-paths", "total": 0}, "industry-news": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=industry-news", "total": 0}, "beginner-s-guide": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=beginner-s-guide", "total": 0}, "casting-faqs": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=casting-faqs", "total": 0}, "awards": {"rel": "article-collection", "href": "https://api.allcasting.com/api/gta/articles?categories=awards", "total": 0}}, "http_status": 200}