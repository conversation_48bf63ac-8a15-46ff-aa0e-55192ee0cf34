import type { Article } from '../../services/endpoints/articles';
type SchemaHowToProps = Exclude<Article['howTo'], null>;

export const formatHowToSchema = ({
  name,
  description,
  cta,
  items,
}: SchemaHowToProps) => ({
  '@context': 'https://schema.org',
  '@type': 'HowTo',
  name,
  text: description,
  step: items.map((item) => ({
    '@type': 'HowToStep',
    name: item.title,
    text: item.description,
  })),
  potentialAction: cta?.link
    ? {
        '@type': 'ViewAction',
        name: cta.text,
        target: cta.link,
      }
    : undefined,
});
