import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FilterMobileHeader,
  Loading,
  FilterPaginator,
  Promo,
  Seo,
  ModalContactTalent,
  AnnouncementBanner,
  ModalProfile,
} from '../../components';
import { MainLayout, PageLayout } from '../../components/Layouts';
import styles from '../../styles/talent.module.scss';
import Filter from '../../components/Filter/Filter';
import FilterService from '@entertech/filter-service';
import Router, { withRouter } from 'next/router';
import Image from 'next/image';
import cn from 'classnames';
import Api from '../../services/api';
import seoPageProps from '../../utils/seoPageProps';
import { useAuth } from '../../contexts/AuthContext';
import { CookieService } from '../../services/cookieService';
import { formatAnnouncement } from '../../utils/formatAnnouncement';
import { getHeaders } from '../../utils/headerHelper';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';

const itemsPerPage = 35;

export const getServerSideProps = async ({ query, req, resolvedUrl, res }) => {
  const content = await Api.serverAPIRoute(
    `/talent/list?filter=${JSON.stringify(query)}`,
    null,
    getHeaders(req, resolvedUrl),
  );

  const page = parseInt(FilterService.getPage(content.filter.data));
  const count = content.data.list.count;
  const pagesTotal = Math.ceil(count / itemsPerPage);

  // elasticsearch issue with pagination
  const maximumPage = 280 > pagesTotal ? pagesTotal : 280;

  const accountLevel = CookieService.getAccountLevelCookie(req, res);

  let formattedAnnouncement = null;

  if (accountLevel?.isPaidOrDelayed) {
    const announcements = (
      await Api.serverAPIRoute(
        `/promotions?types=Announcements&targets=talent-is-paid&limit=1`,
        null,
        getHeaders(req, resolvedUrl),
      )
    ).data;
    const announcement = announcements ? announcements[0] : null;

    formattedAnnouncement = formatAnnouncement(announcement);
  }

  if (maximumPage && page > maximumPage) {
    return {
      redirect: {
        destination: req.url
          .replace(
            `-page-${page}`,
            maximumPage === 1 ? `` : `-page-${maximumPage}`,
          )
          .replace(
            `/page-${page}`,
            maximumPage === 1 ? `` : `/page-${maximumPage}`,
          ),
        permanent: false,
      },
    };
  }

  if (page <= 0 || isNaN(page)) {
    return {
      notFound: true,
    };
  }

  if (page === 1 && req.url.includes(`page-1`)) {
    return {
      redirect: {
        destination: req.url
          .replace(`-page-${page}`, ``)
          .replace(`/page-${page}`, ``),
        permanent: true,
      },
    };
  }

  const promoResponse = await Api.serverAPIRoute(
    `/promotions?targets=guest`,
    null,
    getHeaders(req, resolvedUrl),
  );
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      defaultFilters: await Api.serverAPIRoute(
        `/talent/filters`,
        null,
        getHeaders(req, resolvedUrl),
      ),
      content: content,
      promos: promoResponse.data || [],
      announcement: formattedAnnouncement,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
        ogImageUrl: `${process.env.baseUrl}/assets/meta/talents.webp`,
      },
      page,
      maximumPage,
    },
  };
};

function Talent({
  defaultFilters,
  content,
  router,
  promos,
  page,
  maximumPage = 280,
  seoPage,
  announcement,
}) {
  const {
    list,
    list: { count },
  } = content.data;
  const talentCountLimit = maximumPage * itemsPerPage;

  Router.events.on('routeChangeStart', () => {
    setLoading(true);
  });
  Router.events.on('routeChangeComplete', () => setLoading(false));

  const seo = content.filter.seo;
  const cities = content.filter.cities;
  const [filters, setFilters] = useState(content.filter.data);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showContactTalentModal, setShowContactTalentModal] = useState(false);
  const [isFilterFormOpen, setIsFilterFormOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedProfileId, setSelectedProfileId] = useState(null);
  const [hasFilterChanged, setHasFilterChanged] = useState(
    JSON.stringify(defaultFilters) !== JSON.stringify(filters),
  );
  const { accountLevel } = useAuth();

  const executeScroll = () => {
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    const nextFilter = FilterService.generateFilterFromUrl(
      filters,
      router.query,
      seo,
    );

    setFilters(nextFilter);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleChange = (event, value) => {
    event.preventDefault();

    const nextFilter = FilterService.applyPage(filters, value);

    setFilters(nextFilter);
    router.push(
      '/talent/' + FilterService.generateUrlFromFilter(nextFilter, seo),
      '',
      {
        scroll: false,
      },
    );
    setLoading(true);
    executeScroll();
  };

  const handleFilterElementChangeEvent = (e) => {
    /**
     * Generate a filter object with updated items
     * according to changed element.
     */
    const nextFilter = FilterService.applyElementDataChange(
      e.target.name,
      e.target.value,
      filters,
    );

    setHasFilterChanged(
      JSON.stringify(defaultFilters) !== JSON.stringify(nextFilter),
    );

    /**
     * Set state for data and url
     * for search button click
     */
    setFilters(nextFilter);
  };

  const handleFilterFormSubmitEvent = () => {
    FilterService.applyPage(filters, 1);
    router.push(
      '/talent/' + FilterService.generateUrlFromFilter(filters, seo, 1),
      '',
      {
        scroll: false,
      },
    );
    toggleFilterForm();
    setLoading(true);
    executeScroll();
  };

  const openProfileModal = (id) => {
    setSelectedProfileId(id);
    setShowProfileModal(true);
  };

  const closeProfileModal = () => {
    setShowProfileModal(false);
    setSelectedProfileId(null);
  };

  const openContactTalentModal = () => {
    setShowContactTalentModal(true);
  };

  const closeContactTalentModal = () => {
    setShowContactTalentModal(false);
  };

  const toggleFilterForm = () => {
    setIsFilterFormOpen(!isFilterFormOpen);
  };

  const resetFilter = () => {
    setFilters(defaultFilters);
    setHasFilterChanged(false);
    router.push(
      '/talent/' + FilterService.generateUrlFromFilter(defaultFilters, seo, 1),
      '',
      {
        scroll: false,
      },
    );
    setLoading(true);
    setIsFilterFormOpen(false);
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isUserMenuVisible
      isMobileMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <FilterMobileHeader
          onMobileFilterToggle={toggleFilterForm}
          isFilterFormOpen={isFilterFormOpen}
          showReset={hasFilterChanged}
          resetCallback={resetFilter}
        />
        <div
          className={cn(styles['breadcrumbs'], styles['breadcrumbs-mobile'])}
        >
          <Breadcrumbs crumbs={[{ text: 'Talent Database' }]} />
        </div>
        {announcement && <AnnouncementBanner announcement={announcement} />}
        <Promo promos={promos} />
        <div
          className={cn(styles['breadcrumbs'], styles['breadcrumbs-desktop'])}
        >
          <Breadcrumbs crumbs={[{ text: 'Talent Database' }]} />
        </div>
        <div className={styles.heading}>
          {page > 1 ? (
            <Seo
              seoPage={seoPage}
              overrideTitle={`Talent Database ● allcasting - page ${page}`}
              overrideDescription={`Talent Database ● allcasting - page ${page}`}
              overrideH1={`Talent Database - page ${page}`}
            />
          ) : (
            <Seo seoPage={seoPage} />
          )}
          <div className={styles['title-container']}>
            <div className={styles.title}>
              {count?.toLocaleString('en-US')}
              <span> </span>
              Active Talent
            </div>
          </div>
          {hasFilterChanged && (
            <div onClick={resetFilter} className={styles.reset}>
              reset filters
            </div>
          )}
        </div>
        <div className={styles.content}>
          <div className={styles.left}>
            {loading && (
              <div className={styles.loading}>
                <Loading />
              </div>
            )}
            <div className={styles['talent-container']}>
              {list.items?.map((item, key) => {
                return (
                  <div
                    className={styles.talent}
                    key={key}
                    data-cy="talent-list-item"
                  >
                    <div
                      onClick={() => openProfileModal(item.id)}
                      className={styles['img-container']}
                    >
                      <span
                        className={cn(styles['image'], {
                          [styles['is-placeholder']]: !item.title_photo_url,
                        })}
                        style={{
                          backgroundImage: `url(${
                            item.title_photo_url ||
                            `/assets/placeholders/${
                              item.gender.title.toLowerCase() || 'male'
                            }-head.svg`
                          })`,
                        }}
                      ></span>
                      <div className={styles['talent-details']}>
                        <span className={styles['talent-name']}>
                          {item.firstname}
                        </span>
                        <div className={styles['rating-container']}>
                          <Image
                            className={styles['star']}
                            src={'/assets/icons/icon-star-2.svg'}
                            width={17}
                            height={17}
                            alt="star"
                          />
                          <span>{item.rating?.toLocaleString('en-EN')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <FilterPaginator
              total={count > talentCountLimit ? talentCountLimit : count}
              handleChange={handleChange}
              filters={filters}
              perPage={35}
              seo={seo}
              urlPrefix={'talent'}
            />
          </div>
          <div
            className={cn(styles.right, {
              [styles.open]: isFilterFormOpen,
              [styles['upgrade-header-visible']]:
                accountLevel &&
                (!accountLevel?.isPaidOrDelayed ||
                  !!accountLevel?.canUpgradeExistingSubscription),
            })}
          >
            <Filter
              type="talent"
              data={filters}
              cities={cities}
              onFilterChange={handleFilterElementChangeEvent}
              onFilterSubmit={handleFilterFormSubmitEvent}
            />
          </div>
        </div>
        {showProfileModal && (
          <ModalProfile
            id={selectedProfileId}
            onClose={closeProfileModal}
            onOpenContactTalentModal={openContactTalentModal}
          />
        )}
        {showContactTalentModal && (
          <ModalContactTalent onClose={closeContactTalentModal} />
        )}
      </PageLayout>
    </MainLayout>
  );
}

export default withRouter(Talent);
