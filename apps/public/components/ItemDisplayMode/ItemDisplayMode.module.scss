@import '../../styles/variables';
@import '../../styles/mixins';

.format {
  display: none;

  @include tablet {
    line-height: 1;
    display: flex;
    margin-left: auto;
  }
}

.item {
  cursor: pointer;
  color: $grey-100;
  font-size: 12px;
  display: flex;
  margin-right: $space-20;
  opacity: 0.5;

  &:hover {
    opacity: 0.8;
  }
}

.list {
  &::before {
    margin-right: $space-10;
    flex-shrink: 0;
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    background: url('#{$assetUrl}/assets/icons/icon-list.svg') center center
      no-repeat;
    background-size: cover;
  }
}

.grid {
  &::before {
    margin-right: $space-10;
    flex-shrink: 0;
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    background: url('#{$assetUrl}/assets/icons/icon-grid.svg') center center
      no-repeat;
    background-size: cover;
  }
}

.active {
  opacity: 1;
}
