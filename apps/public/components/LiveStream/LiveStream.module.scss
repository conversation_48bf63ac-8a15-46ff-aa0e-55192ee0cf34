@import '../../styles/mixins';
@import '../../styles/variables';

.container {
  position: fixed;
  bottom: 60px;
  right: 0;
  background-color: $white;
  box-sizing: border-box;
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: row;
  z-index: 10;
  overflow: hidden;

  @include tablet {
    height: 200px;
  }

  @include desktop {
    flex-direction: column;
    bottom: 20px;
    right: 20px;
    border-radius: 10px;
    width: 400px;
    height: 320px;
    box-shadow: $shadow-card-container;
  }
}

.video {
  position: relative;
  display: flex;

  iframe {
    border: 0;
    height: 100px;
    max-width: 150px;
    overflow: hidden;

    @include tablet {
      max-width: 400px;
      height: 200px;
    }

    @include desktop {
      width: 100%;
      height: 240px;
    }
  }
}

.description {
  padding: $space-10 $space-20;
}

.webinar {
  color: $grey-100;
  font-size: 14px;
  font-weight: 600;

  &::before {
    content: '';
    background: url('#{$assetUrl}/assets/icons/icon-broadcast.svg') left center
      no-repeat;
    padding: $space-10;
    margin-right: $space-5;
  }
}

.title {
  font-size: 15px; // font-size: 16px;
  font-weight: 600; // font-weight: 700;
  margin-top: $space-5;
}

.close-button-container {
  padding: $space-15;
  width: 100%;
}

.close-button {
  background-color: $grey-10;
  border-radius: 50%;
  border-style: none;
  cursor: pointer;
  outline: none;
  position: absolute;
  right: 14px;
  top: 8px;
  height: 30px;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
  width: 30px;
  z-index: 10;

  @include desktop {
    top: 14px;
  }

  &:hover {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
  }

  &::before {
    background-color: $black;
    transform: rotate(45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }

  &::after {
    background-color: $black;
    transform: rotate(-45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }
}

.live-badge {
  display: none;
  background-color: $red-100;
  border-radius: 15px;
  color: $white;
  position: absolute;
  left: 14px;
  top: 14px;
  padding: 2px 14px 3px;
  font-size: 14px;
  z-index: 10;
  text-align: center;

  @include desktop {
    display: block;
  }
}
