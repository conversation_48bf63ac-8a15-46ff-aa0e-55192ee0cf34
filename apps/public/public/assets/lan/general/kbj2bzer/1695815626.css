/*! normalize.css v3.0.2 | MIT License | git.io/normalize */

/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */

html {
    font-family: sans-serif;
    /* 1 */
    -ms-text-size-adjust: 100%;
    /* 2 */
    -webkit-text-size-adjust: 100%;
    /* 2 */
}

/**
 * Remove default margin.
 */

body {
    margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */

/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
    display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */

audio, canvas, progress, video {
    display: inline-block;
    /* 1 */
    vertical-align: baseline;
    /* 2 */
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */

audio:not([controls]) {
    display: none;
    height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/11, Safari, and Firefox < 22.
 */

[hidden], template {
    display: none;
}

/* Links
   ========================================================================== */

/**
 * Remove the gray background color from active links in IE 10.
 */

a {
    background-color: transparent;
}

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */

a:active, a:hover {
    outline: 0;
}

/* Text-level semantics
   ========================================================================== */

/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */

abbr[title] {
    border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */

b, strong {
    font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */

dfn {
    font-style: italic;
}

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */

h1 {
    font-size: 2em;
    margin: 0.67em 0;
}

/**
 * Address styling not present in IE 8/9.
 */

mark {
    background: #ff0;
    color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */

small {
    font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove border when inside `a` element in IE 8/9/10.
 */

img {
    border: 0;
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */

svg:not(:root) {
    overflow: hidden;
}

/* Grouping content
   ========================================================================== */

/**
 * Address margin not present in IE 8/9 and Safari.
 */

figure {
    margin: 1em 40px;
}

/**
 * Address differences between Firefox and other browsers.
 */

hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
}

/**
 * Contain overflow in all browsers.
 */

pre {
    overflow: auto;
}

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */

code, kbd, pre, samp {
    font-family: monospace, monospace;
    font-size: 1em;
}

/* Forms
   ========================================================================== */

/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */

/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */

button, input, optgroup, select, textarea {
    color: inherit;
    /* 1 */
    font: inherit;
    /* 2 */
    margin: 0;
    /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */

button {
    overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */

button, select {
    text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */

button, html input[type="button"], input[type="reset"], input[type="submit"] {
    -webkit-appearance: button;
    /* 2 */
    cursor: pointer;
    /* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */

button[disabled], html input[disabled] {
    cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */

button::-moz-focus-inner, input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */

input {
    line-height: normal;
}

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */

input[type="checkbox"], input[type="radio"] {
    box-sizing: border-box;
    /* 1 */
    padding: 0;
    /* 2 */
}

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome
 *    (include `-moz` to future-proof).
 */

input[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    /* 2 */
    box-sizing: content-box;
}

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

/**
 * Define consistent border, margin, and padding.
 */

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */

legend {
    border: 0;
    /* 1 */
    padding: 0;
    /* 2 */
}

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */

textarea {
    overflow: auto;
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */

optgroup {
    font-weight: bold;
}

/* Tables
   ========================================================================== */

/**
 * Remove most spacing between table cells.
 */

table {
    border-collapse: collapse;
    border-spacing: 0;
}

td, th {
    padding: 0;
}

html {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

*, *:before, *:after {
    -webkit-box-sizing: inherit;
    -moz-box-sizing: inherit;
}

input, textarea {
    outline: none;
}

*:focus {
    outline: none;
}

textarea, input {
    -webkit-appearance: none;
}

textarea:focus, input:focus {
    outline: none;
}

textarea {
    resize: none;
}

/*
 * Re-set default cursor for disabled elements
 */

button[disabled], input[disabled] {
    cursor: default;
}

/*
 * Remove inner padding and border in FF3/4: h5bp.com/l
 */

button::-moz-focus-inner, input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

*:focus {
    outline: none;
}

textarea, input {
    -webkit-appearance: none;
}

ul {
    list-style: none;
}

h1, h2, h3, h4, h5 {
    margin: 0;
}

.clearfix:before, .clearfix:after {
    content: "\0020";
    display: block;
    height: 0;
    overflow: hidden;
}

.clearfix:after {
    clear: both;
}

.clearfix {
    zoom: 1;
}

html, body {
    font-family: 'Poppins', Arial, Helvetica, sans-serif;
    font-size: 16px;
    color: #000;
    line-height: 1.3;
    background-color: #fff;
}

html {
    /*background-color: #fff;*/
}

body {
    min-height: 100%;
    overflow-y: scroll;
    position: relative;
}

*:focus {
    outline: none;
}

a {
    text-decoration: none;
    color: #3399cc;
}
/* main elements */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden-box {
    display: none;
}

.page-body {
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
    max-width: 1240px;
}

.btn {
    display: inline-block;
    padding: 16px 20px;
    margin-bottom: 0;
    font-size: 18px;
    color: #fff;
    font-weight: 700;
    line-height: 1.35;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-size: 200% auto;
    background-position: 0 0;
    background: #F50021;
    background-image: linear-gradient(90deg, #FE3333 0%, #F50021 50%, #C20018 100%, #C20018 200%);
    box-shadow: 0 8px 20px rgba(179, 0, 26, 0.3);
    border: none;
    border-radius: 30px;
    background-size: 200% auto;
    background-position: 0 50%;
    -moz-transition: all 0.3s linear 0s;
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.btn:hover {
    cursor: pointer;
    background-position: 100% 50%;
    box-shadow: 0 4px 20px rgba(153, 0, 19, 0.3);
}
.btn-cc {
    position: relative;
    padding: 12px 57px 12px 20px;
    font-size: 12px;
    background: #FEE5E8;
    border-radius: 30px;
    box-shadow: none;
    color: #993D4A;
    text-transform: uppercase;
}
.btn-cc-arrow {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 20px;
    width: 15px;
    height: 16px;
}
.btn-cc-arrow::before {
    content: "";
    position: absolute;
    top: 4px;
    right: 0;
    margin: 0 auto;
    width: 50%;
    height: 2px;
    background-color: #993D4A;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.btn-cc-arrow::after {
    content: "";
    position: absolute;
    bottom: 5px;
    right: 0;
    margin: 0 auto;
    width: 50%;
    height: 2px;
    background-color: #993D4A;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.btn-cc:hover {
    color: #fff;
    background-color: #4D2E32;
}
.btn-cc:hover .btn-cc-arrow::before,
.btn-cc:hover .btn-cc-arrow::after {
    background-color: #fff;
}

.btn-loading {
    display: inline-block;
    border-width: 2px;
    border-color: currentColor;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-style: solid;
    border-radius: 9999px;
    color: currentColor;
    -webkit-animation: animation-d2btug 0.45s linear infinite;
    animation: animation-d2btug 0.45s linear infinite;
    width: 23px;
    height: 23px;
    position: absolute;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    display: none;
}
.btn.loading {
    position: relative;
    opacity: 0.5;
    cursor: not-allowed;
}
.btn.loading:hover {
    cursor: not-allowed;
}
.btn.loading .btn-loading {
    display: block;
}
.btn.loading .btn-text {
    visibility: hidden;
}

.btn_block {
    display: block;
    width: 100%;
}
.btn:disabled,
.btn[disabled] {
    cursor: default;
    opacity: 0.5;
}
.btn:disabled:hover,
.btn[disabled]:hover {
    background-size: auto;
}

/* spinner */
.index-spinner-bar {
    display: flex;
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    align-content: center;
    padding: 20px 0;
    text-align: center
}
.index-spinner-bar:not(.active) {
    display: none;
}

.index-spinner-viewer {
    width: 47px;
    height: 22px;
    margin: auto;
    letter-spacing: -.3em
}

.index-spinner-viewer-bar {
    display: inline-block;
    letter-spacing: normal;
    width: 4px;
    height: 100%;
    margin: 0 2px;
    background-color: #641971;
    -webkit-animation: index-spinner 1s ease-in-out infinite;
    animation: index-spinner 1s ease-in-out infinite;
    will-change: transform
}

.index-spinner-viewer-bar:first-child {
    -webkit-animation-delay: 0s;
    animation-delay: 0s
}

.index-spinner-viewer-bar:nth-child(2) {
    -webkit-animation-delay: .09s;
    animation-delay: .09s
}

.index-spinner-viewer-bar:nth-child(3) {
    -webkit-animation-delay: .18s;
    animation-delay: .18s
}

.index-spinner-viewer-bar:nth-child(4) {
    -webkit-animation-delay: .27s;
    animation-delay: .27s
}

.index-spinner-viewer-bar:nth-child(5) {
    -webkit-animation-delay: .36s;
    animation-delay: .36s
}
@keyframes index-spinner {
    0% {
        transform: scale(1)
    }

    20% {
        transform: scaleY(1.5)
    }

    40% {
        transform: scale(1)
    }
}

@-webkit-keyframes index-spinner {
    0% {
        transform: scale(1)
    }

    20% {
        transform: scaleY(1.5)
    }

    40% {
        transform: scale(1)
    }
}


/*forms*/
.fieldsend {
    position: relative;
    margin-top: 27px;
    box-sizing: border-box;
}
.fieldsend-password-hint {
    margin-top: 1px;
    font-size: 10px;
    color: #666;
    font-weight: 300;
}
.fieldsend-placeholder {
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    color: #999;
    font-weight: 400;
    font-family: 'Open Sans', Sans-Serif;
    font-size: 16px;
    -moz-transition: all .25s ease;
    -webkit-transition: all .25s ease;
    transition: all .25s ease;
    cursor: text;
}

.fieldsend-placeholder span {
    display: inline-block;
    color: #e13d96;
    vertical-align: top
}

.fieldsend-placeholder.field-focus {
    font-size: 12px;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
}

.input-field {
    position: relative;
    display: block;
    vertical-align: top;
    box-sizing: border-box;
}

.input-field .fieldsend-placeholder {
    top: 18px;
}
.input-field .decorator {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
}
.input-field .decorator:before {
    height: 1px;
    background-color: #999;
    bottom: 0;
    content: '';
    display: block;
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
}
.input-field .decorator:after {
    height: 1px;
    background-color: #999;
    bottom: 0;
    content: '';
    display: block;
    left: 0;
    opacity: .15;
    pointer-events: none;
    position: absolute;
    -moz-transition: width 0.3s ease-in-out 0s;
    -webkit-transition: width 0.3s ease-in-out 0s;
    transition: width 0.3s ease-in-out 0s;
    width: 0;
}
.input-error-text {
    position: absolute;
    bottom: -20px;
    display: none;
    color: #f53434;
    font-size: 12px;
}
.input-error-checkbox {
    position: relative;
    bottom: 0;
}
.input-field input {
    position: relative;
    display: block;
    width: 100%;
    padding: 14px 0 7px;
    line-height: 1.3;
    color: #353535;
    font-family: 'Open Sans', Sans-Serif;
    background: transparent;
    border: none;
    font-size: 16px;
    box-sizing: border-box;
    z-index: 1
}

.input-field input.not-empty~.fieldsend-placeholder, .input-field input:focus~.fieldsend-placeholder {
    font-size: 12px;
    top: 0;
    opacity: 1;
}

.input-field input.not-empty~.decorator::after, .input-field > input:focus~.decorator::after {
    width: 100%;
}

.input-field input.is-value~.fieldsend-placeholder {
    font-size: 10px;
    top: 0;
    opacity: .7;
}

.input-field input.is-value:focus~.fieldsend-placeholder {
    opacity: 1;
}

.input-field__text {
    padding-top: 4px;
    font-size: 12px;
}

.input-field_s input {
    font-size: 14px;
}

.input-field_s input:focus~.fieldsend-placeholder {
    font-size: 10px;
}

.input-field_s .fieldsend-placeholder {
    font-size: 14px;
    font-style: italic;
}


.field-label-checkbox {
    position: relative;
    display: block;
    padding: 0 0 0 25px;
    overflow: visible;
    cursor: pointer;
}

.field-label-checkbox input[type=checkbox] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

.field-label-checkbox__check {
    position: absolute;
    top: 2px;
    left: 0;
    width: 16px;
    height: 16px;
    border: 1px solid #ebebeb;
}

.field-label-checkbox input[type=checkbox]:checked~.field-label-checkbox__checked {
    position: absolute;
    width: 10px;
    height: 8px;
    top: 6px;
    left: 3px;
}

.field-label-checkbox input[type=checkbox]:checked~.field-label-checkbox__checked:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    margin: 0 auto;
    width: 4px;
    height: 1px;
    background-color: #b3a0a0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

.field-label-checkbox input[type=checkbox]:checked~.field-label-checkbox__checked:after {
    content: "";
    position: absolute;
    top: 0;
    left: 1px;
    margin: 0 auto;
    width: 7px;
    height: 1px;
    background-color: #410e74;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.field-label-checkbox__txt {
    word-wrap: break-word;
}

.field-label-checkbox_s {
    padding: 0 0 0 20px;
}

.field-label-checkbox_s .field-label-checkbox__check {
    top: 4px;
    width: 15px;
    height: 15px;
    border: 1px solid #565656;
}

.field-label-checkbox_s input[type=checkbox]:checked~.field-label-checkbox__checked {
    width: 10px;
    height: 7px;
    top: 6px;
    left: 3px;
}

.field-label-checkbox_s input[type=checkbox]:checked~.field-label-checkbox__checked:before {
    background-color: #410e74;
}

.field-label-checkbox_s input[type=checkbox]:checked~.field-label-checkbox__checked:after {
    background-color: #410e74;
}

.input-hint {
    position: relative;
}

.input-field__password-type {
    position: absolute;
    align-items: flex-end;
    justify-content: flex-end;
    width: 40px;
    right: 0;
    background-color: #fff;
    color: #afafaf;
    font-size: 12px;
    font-weight: 700;
    display: none;
    z-index: 1;
}

.password-strength__ok {
    display: flex;
    color: #1fe3d7;
}
.password-strength__weak {
    display: flex;
    color: #f31616;
}
.password-strength__normal {
    display: flex;
    color: #f2bc00;
}
.password-strength__strong {
    display: flex;
    color: #2bb672;
}

.input-field__show-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 1px;
    width: 40px;
    cursor: pointer;
    z-index: 1;
}
.input-field__show-icon {
    width: 18px;
    color: #777;
    height: 1em;
}
.input-field__show-btn.show .input-field__show-icon::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 22px;
    height: 3px;
    margin: auto;
    background-color: #777;
    border-bottom: 2px solid #fff;
    transform: rotate(-45deg);
}
.input-field__show-btn:hover .input-field__show-icon {
    color: #410e74
}
.input-field__show-icon svg {
    display: block;
    height: 100%;
    max-width: 100%;
    width: auto;
}
.input-field__show-icon:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: block;
    -moz-transform: translateY(50%);
    -webkit-transform: translateY(50%);
    transform: translateY(50%);
    width: 30px;
    height: 14px;
    margin: auto;
    background-size: contain;
}
.input-field__show-icon.show:after {
    background-size: contain;
}
.input-field__password.show-type-forgot .input-field__password-type.forgot {
    display: block;
}

.input-field__password.show-type .input-field__password-type {
    display: flex;
}
.has-error ~ .input-error-text {
    display: block;
}
.has-error input {
    border-bottom-color: #f53434;
}
.has-error .fieldsend-placeholder {
    color: #f53434;
}
.has-error .decorator::after,
.has-error .decorator::before {
    background-color: #f53434;
}
label.has-error {
    color: #f53434;
}
.has-error.field-label-checkbox__check {
    border-color: #f53434;
}
.form-error {
    color: #f53434;
    text-align: center;
    font-size: 14px;
    padding: 0 15px 20px;
}
.form-notification {
    display: block;
    box-shadow: 0 2px 18px rgba(0,0,0,.15);
    border-radius: 5px;
    background-color: #fff;
    color: #777;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.43;
    padding: 10px 6px;
    text-align: center;
}


/* layout */
.header-section {
    display: flex;
    flex-flow: column nowrap;
    justify-content: stretch;
    position: relative;
    margin-bottom: 30px;
    background: url('/assets/lan/general/kbj2bzer/map.png') 21% -14px no-repeat;
    background-size: cover;
    z-index: 1;
}
.header-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 30px;
    background: url('/assets/lan/general/kbj2bzer/city.png') 85% 100% no-repeat;
    background-size: cover;
}
.header-body {
    display: flex;
    flex-grow: 1;
    justify-content: center;
}
.header-logo {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    width: 100px;
    height: 23px;
    z-index: 1;
}
.header-logo svg {
    height: 100%;
    width: 100%;
    color: #040404;
    opacity: 0.5;
}
.header-section-img-pc {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/assets/lan/general/kbj2bzer/ca-light-girl-mob.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50% 100%;
    display: none;
}
.header-section-img-mob {
    max-width: 100%;
    margin: auto auto -50px;
    overflow: hidden;
}
.header-section-img-mob img {
    display: block;
    width: 150%;
    margin-left: -25%;
}
.header-section-img-mob::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -20px;
    right: -20px;
    height: 26%;
    background: linear-gradient(0deg, #F3F3F3 0%, rgba(242, 242, 242, 0) 100%);
}
.header-side-main {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    text-align: center;
    justify-content: space-between;
    min-height: 400px;
}
.header-title {
    font-style: normal;
    font-size: 26px;
    line-height: 1.35;
    padding-top: 26px;
    color: #040404;
    font-weight: 400;
}
.header-btn .btn {
    max-width: 240px;
    transform: translateY(50%);
    margin-left: auto;
    margin-right: auto;
    font-size: 14px;
    padding: 17px 20px;
    text-transform: uppercase;
    font-family: "Open Sans", Sans-Serif;
}

.footer-side {
    padding: 35px 0;
    font-weight: 300;
    text-align: center;
}
.footer-side p {
    margin: 0;
    font-size: 14px;
    color: #777;
    line-height: 1.5;
}
.footer-side p:not(:last-child) {
    margin-bottom: 22px;
}

.brand-panel {
    position: relative;
    height: 76px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-bottom: 1px solid #C4C4C4;
}
.brand-item {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 500;
    line-height: 1;
    letter-spacing: 1.5px;
    color: #040404;
}
.brand-item:not(:last-child)::after {
    content: "";
    display: block;
    margin: 0 6px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #9A9A9A;
}



/* BENEFITS side */
.benefit-section {
    position: relative;
    padding: 42px 0 40px;
    z-index: 1;
}
.benefit-section-side {
    display: grid;
    grid-gap: 34px;
    align-items: center;
    padding: 0 14px 50px;
}
.benefit-block {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    box-sizing: border-box;
    width: 100%;
}
.benefit-block-icon {
    position: relative;
    width: 62px;
    height: 62px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 18px;
    background-color: #FFF2F4;
    border-radius: 50%;
}
.benefit-block-icon::before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}
.benefit-block-icon-1::before {
    width: 27px;
    height: 24px;
    background: url('/assets/lan/general/kbj2bzer/ca-light-benefit_1.svg') 0 0 no-repeat;
    background-size: contain;
}
.benefit-block-icon-2::before {
    width: 26px;
    height: 26px;
    background: url('/assets/lan/general/kbj2bzer/ca-light-benefit_2.svg') 0 0 no-repeat;
    background-size: contain;
}
.benefit-block-icon-3::before {
    width: 24px;
    height: 24px;
    background: url('/assets/lan/general/kbj2bzer/ca-light-benefit_3.svg') 0 0 no-repeat;
    background-size: contain;
}
.benefit-block-icon-4::before {
    width: 27px;
    height: 26px;
    background: url('/assets/lan/general/kbj2bzer/ca-light-benefit_4.svg') 0 0 no-repeat;
    background-size: contain;
}
.benefit-block-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
    color: #040404;
}
.benefit-block-text {
    color: #262626;
    line-height: 1.5;
    font-weight: 400;
    text-align: center;
}
.benefit-btn-side .btn {
    max-width: 240px;
    margin-left: auto;
    margin-right: auto;
}
/* end BENEFITS side */

/* CC side */
.cc-section {
    position: relative;
    padding: 35px 0 0;
    background: linear-gradient(180deg, #E6E6E6 0%, #F3F3F3 100%);
}
.cc-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 48%;
    height: 100%;
    background-image: url('/assets/lan/general/kbj2bzer/cc-bg-rectangle.svg');
    background-position: 100% 0;
    background-repeat: no-repeat;
    background-size: cover;
}
.cc-section-title {
    position: relative;
    color: #040404;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 43px;
    text-align: center;
}
#dynamic-content-widget .cc-section-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
}
#dynamic-content-widget .cc-filter-row {
    position: relative;
    display: none;
    flex-flow: row nowrap;
}
#dynamic-content-widget .cc-filter-item {
    padding: 9px 23px;
    opacity: 0.6;
    border-radius: 40px;
    color: #343434;
    border: 1px solid #343434;
    transition: all 0.3s linear;
    cursor: pointer;
}
#dynamic-content-widget .cc-filter-item:hover {
    opacity: 1;
}
#dynamic-content-widget .cc-filter-item.isActive {
    background-color: #fff;
    color: #262626;
    border-color: #fff;
    opacity: 1;
}
#dynamic-content-widget .cc-filter-item-wrap:not(:last-child) {
    margin-right: 25px;
}
#dynamic-content-widget .cc-filter-select {
    position: relative;
    padding: 0;
    border-radius: 40px;
    background: #fff;
}
#dynamic-content-widget .cc-filter-select select {
    width:100%;
    margin:0;
    background: none;
    border: none;
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    font-size: 16px;
    color: #262626;
    padding: 13px 38px 13px 20px;
    line-height: 1.35;
}
#dynamic-content-widget .cc-filter-select::after {
    content: "";
    position: absolute;
    width: 14px;
    height: 9px;
    top: 50%;
    right: 20px;
    margin-top: -4px;
    z-index: 2;
    background-image: url("data:image/svg+xml,%3Csvg width='14' height='9' viewBox='0 0 14 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 1.87634L7 7.12109L2 1.87634' stroke='black' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    pointer-events:none;
}
#dynamic-content-widget select:focus {
    background: transparent;
    color: #222;
}
#dynamic-content-widget .cc-section-wrapper {
    margin: 0 0 40px;
}
#dynamic-content-widget .cc-section-list {
    position: relative;
    display: grid;
    grid-row-gap: 30px;
    justify-content: center;
}
#dynamic-content-widget .cc-box-wrap {
    margin: 0 5px;
}
#dynamic-content-widget .cc-box {
    display: flex;
    flex-flow: column nowrap;
    padding: 33px 32px 32px;
    background: #FFF;
    box-shadow: 0 0 50px rgba(144, 144, 144, 0.2);
    border-radius: 6px;
}
#dynamic-content-widget .cc-box-details {
    display: inline-flex;
    align-items: center;
    background: #FAFAFA;
    border-radius: 40px;
    padding: 0 20px 0 0;
    margin-bottom: 23px;
    margin-right: auto;
}
#dynamic-content-widget .cc-box-icon {
    position: relative;
    display: flex;
    min-width: 32px;
    height: 32px;
    margin-right: 13px;
    border-radius: 50px;
}
#dynamic-content-widget .cc-box-icon::after {
    content: "";
    position: absolute;
    display: flex;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    margin: auto;
}
#dynamic-content-widget .cc-box-hot {
    display: flex;
    align-items: center;
    color: #F75D5D;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 600;
}
#dynamic-content-widget .cc-box-hot::after {
    content: "";
    display: flex;
    width: 5px;
    height: 5px;
    margin: 0 5px;
    border-radius: 50%;
    background-color: #C4C4C4;
}
#dynamic-content-widget .cc-box-type {
    display: flex;
    font-size: 14px;
    font-weight: 300;
}
#dynamic-content-widget .cc-box-title {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    line-height: 1.35;
    letter-spacing: 0.2px;
    margin-bottom: 22px;
}
#dynamic-content-widget .cc-box-location {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #3E3E3E;
    font-weight: 400;
    margin-bottom: 25px;
}
#dynamic-content-widget .cc-box-location::before {
    content: "";
    width: 12px;
    height: 14px;
    margin-right: 7px;
    background: url('/assets/lan/general/kbj2bzer/location-icon.svg') 0 0 no-repeat;
}
#dynamic-content-widget .btn-cc {
    margin-top: auto;
    margin-right: auto;
}
#dynamic-content-widget .cc-slider-btn {
    position: relative;
    width: 52px;
    height: 52px;
    background: rgba(255, 255, 255, 0.3);
    border: none;
    transition: all ease-in-out 0.3s;
    cursor: default;
}
#dynamic-content-widget .cc-slider-left-arrow {
    position: absolute;
    left: 14px;
}
#dynamic-content-widget .cc-slider-right-arrow {
    position: absolute;
    left: 17px;
}
#dynamic-content-widget .cc-slider-left-arrow::before,
#dynamic-content-widget .cc-slider-left-arrow::after,
#dynamic-content-widget .cc-slider-right-arrow::before,
#dynamic-content-widget .cc-slider-right-arrow::after {
    background: rgba(38, 38, 38, 0.5);
    transition: all ease-in-out 0.3s;
}
#dynamic-content-widget .cc-slider-btn.active {
    background: rgba(255, 255, 255);
    cursor: pointer;
}
#dynamic-content-widget .cc-slider-btn.active:hover {
    background: rgba(255, 255, 255, 0.6);
}
#dynamic-content-widget .cc-slider-btn.active .cc-slider-left-arrow::before,
#dynamic-content-widget .cc-slider-btn.active .cc-slider-left-arrow::after,
#dynamic-content-widget .cc-slider-btn.active .cc-slider-right-arrow::before,
#dynamic-content-widget .cc-slider-btn.active .cc-slider-right-arrow::after {
    background-color: #262626;
}
#dynamic-content-widget .cc-slider-btn.active:hover .cc-slider-left-arrow::before,
#dynamic-content-widget .cc-slider-btn.active:hover .cc-slider-left-arrow::after,
#dynamic-content-widget .cc-slider-btn.active:hover .cc-slider-right-arrow::before,
#dynamic-content-widget .cc-slider-btn.active:hover .cc-slider-right-arrow::after {
    background: rgba(38, 38, 38, 0.8);
}
#dynamic-content-widget .cc-loader-line {
    background-color: red;
}
.cc-btn-side {
    transform: translateY(50%);
}
.cc-btn-side .btn {
    max-width: 240px;
    margin: 0 auto;
}
/* end CC side */

/*  TOOLS side */
.tools-section {
    padding: 83px 0 45px;
}
.tools-section .page-body {
    position: relative;
    overflow: hidden;
}

.tools-title {
    font-size: 32px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 47px;
    color: #262626;
    letter-spacing: 0.5px;
}
.tools-section-area {
    margin: 0 0 45px;
}
.tools-block {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: flex-start;
    padding: 0 4px 0 94px;
}
.tools-block:not(:last-child) {
    padding-bottom: 35px;
}
.tools-block-icon {
    position: absolute;
    top: -9px;
    left: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 64px;
    height: 64px;
    background-color: #fff;
    border-radius: 50%;
    font-weight: 600;
    font-size: 20px;
    color: #262626;
    box-shadow: 0 0 30px rgba(157, 168, 179, 0.4);
    z-index: 1;
}
.tools-block:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 0;
    left: 36px;
    bottom: 0;
    border-left: 1px dashed #E6A1AA;
}
.tools-block-title {
    color: #040404;
    font-weight: 600;
    line-height: 1.5;
    font-size: 20px;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}
.tools-block-text {
    color: #666666;
    font-weight: 400;
    line-height: 1.5;
    font-size: 18px;
}
.tools-block-title span {
    color: #FF001F;
    cursor: pointer;
}
.tools-block-image {
    position: relative;
    margin: -17px 0 0;
    overflow: hidden;
    display: none;
}
.tools-block-image img {
    display: block;
    max-width: 100%;
    max-height: 100%;
    margin-left: -10px;
}
.tools-section-main {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
}
.tools-btn-side {
}
.tools-btn-side .btn {
    max-width: 240px;
    margin-left: auto;
    margin-right: auto;
}
/* end TOOLS side */


/* REVIEWS side */
.reviews-section {
    position: relative;
    background: linear-gradient(180deg, #E6E6E6 0%, #F3F3F3 100%);
    padding-top: 54px;
}
.reviews-section::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: url('/assets/lan/general/kbj2bzer/reviws-bg-rectangle.svg');
    background-position: 238px 16%;
    background-repeat: no-repeat;
}
.reviews-section-title {
    position: relative;
    font-size: 32px;
    margin-bottom: 25px;
    letter-spacing: 0.5px;
    color: #331418;
    text-align: center;
    font-weight: 600;
    line-height: 1.18;
}
.reviews-section-box {
    display: grid;
    grid-row-gap: 25px;
    position: relative;
    margin: 0 5px 15px;
}
.review-block {
    position: relative;
    display: flex;
    margin: 0 auto;
    padding-top: 50px;
}
.review-block-body {
    position: relative;
    display: flex;
    flex-grow: 1;
    flex-flow: column nowrap;
    padding: 56px 26px 46px;
    background: #FFF;
    box-shadow: 0 8px 40px rgba(144, 144, 144, 0.2);
    border-radius: 10px;
    z-index: 1;
}
.review-block-img-box {
    position: absolute;
    top: -8px;
    left: 40px;
    transform: translateY(-50%);
    display: flex;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-left: auto;
    margin-right: auto;
    flex-grow: 0;

}
.review-block-img-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: #FFFFFF;
    border: 2px solid #FFCCD4;
}
.review-block-img {
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    background-size: contain;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    border-radius: 50%;
}
.review-block-main {

}
.review-block-name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 3px;
    color: #1D1D1D;
    line-height: 1.2;
    letter-spacing: 0.5px;
}
.review-block-info {
    font-weight: 400;
    line-height: 1.5;
    color: #666;
    margin-bottom: 21px;
}
.review-block-text {
    font-weight: 400;
    line-height: 1.5;
    color: #666;
}
.review-btn-side {
    transform: translateY(50%);
}
.review-btn-side .btn {
    max-width: 240px;
    margin-left: auto;
    margin-right: auto;
}
    /* end REVIEWS side */

    /*  ABOUT side */
.about-section {
    padding: 80px 0 55px;
}
.about-section-title {
    font-size: 32px;
    color: #262626;
    font-weight: 600;
    letter-spacing: 0.5px;
    line-height: 1.4;
    margin-bottom: 20px;
}
.about-section-area {
    max-width: 790px;
    margin: auto;
}
.about-block {
    border-bottom: 1px solid #c4c4c4;
}
.about-block:last-child {
    border-bottom: none;
}
.about-block-heading {
    position: relative;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    padding: 25px 35px 20px 0;
}
.about-block-arrow {
    position: absolute;
    top: 28px;
    right: 4px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 2;
}
.about-block-arrow::before {
    content: "";
    position: absolute;
    top: auto;
    bottom: 46%;
    right: 0;
    margin: 0 auto;
    width: 12px;
    height: 3px;
    background-color: #000;
    transform: rotate(-45deg);
    transition: all 0.3s linear 0s;
}
.about-block-arrow::after {
    content: "";
    position: absolute;
    bottom: 46%;
    left: 0;
    right: auto;
    margin: 0 auto;
    width: 12px;
    height: 3px;
    background-color: #000;
    transform: rotate(45deg);
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.about-block-title {
    font-size: 20px;
    font-weight: 400;
    color: #262626;
    line-height: 1.3;
}
.about-block-body {
    height: 0;
    overflow: hidden;
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.about-block-body-text {
    padding: 0 35px 40px 0;
    transform: scaleY(0);
    -webkit-transition: all 0.3s linear 0s;
    transition: all 0.3s linear 0s;
}
.about-block-body-text p {
    margin: 0 0 10px;
    color: #666;
    line-height: 1.5;
    font-weight: 400;
}
.about-block-body-text p:last-child {
    margin-bottom: 0;
}
.about-block-image {
    display: flex;
    justify-content: flex-end;
}
.about-block-image img {
    display: block;
    margin-top: 17px;
    max-width: 100%;
    max-height: 100%;
}

.about-block.isOpen .about-block-title {
    font-weight: 600;
    letter-spacing: 0.3px;
}
.about-block.isOpen .about-block-arrow::before {
    top: 46%;
    bottom: auto;
    transform: rotate(45deg);
}
.about-block.isOpen .about-block-arrow::after {
    top: 46%;
    bottom: auto;
    transform: rotate(-45deg);
}
.about-block.isOpen .about-block-body {
    height: auto;
}
.about-block.isOpen .about-block-body-text {
    transform: scaleY(1);
}
.about-block-image.isHidden {
    display: none;
}
.about-btn-side {
    margin-top: 45px;
}
.about-btn-side .btn {
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
}
    /* end ABOUT side */

.section-form {
    position: relative;
    padding: 40px 0 0;
}
.section-form-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 218px;
    background: linear-gradient(180deg, #E6E6E6 0%, #F3F3F3 100%);
    overflow: hidden;
}
.section-form-bg::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/lan/general/kbj2bzer/kc.ca-final-form-bg.svg') 58% 0 no-repeat;
    transform: rotate(90deg);
}
.section-form-body {
    display: grid;
}
.section-form-title {
    margin-bottom: 36px;
    font-size: 32px;
    font-weight: 700;
    color: #262626;
    text-align: center;
    letter-spacing: 0.8px;
    line-height: 1.1;
    order: 1;
}
.form {
    position: relative;
    max-width: 587px;
    padding: 6px 0 50px;
    margin-left: -10px;
    margin-right: -10px;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    order: 2;
}
.section-form .page-body {
    position: relative;
}

.form-body {
    padding: 0 35px;
}
.form-disclaimer {
    padding: 40px 35px 37px;
}
.form-disclaimer p {
    margin: 0;
    line-height: 1.5;
    font-size: 14px;
    color: #999;
    font-family: 'Open Sans', Sans-Serif;
}
.form-disclaimer p:not(:last-child) {
   margin-bottom: 10px;
}

.form-disclaimer a, .link-blue {
    color: #222;
}
.form-disclaimer a:hover {
    text-decoration: underline;
}

.signup_error {
    color: #F00;
    font-size: 13px;
    text-align: center;
    margin-top: 15px;
}
.form-btn-panel .btn {
    max-width: 247px;
    margin: 0 auto;
}

.form-error-container {
    display: block;
    margin: 0;
    padding: 0;
}
.form-error-container a {
    color: #f75d5d;
    font-weight: 700;
    text-decoration: underline;
}
.form-error {
    color: #f75d5d;
    text-align: center;
    font-size: 14px;
    padding: 0 15px 20px;
}
/* END fORM STYLES */

@media screen and (min-width: 768px) {
    .form {
        margin-left: auto;
        margin-right: auto;
    }
    .is-hidden-desktop {
        display: none;
    }
}
@media screen and (max-width: 1024px) {
    .is-visible-desktop {
        display: none;
    }
    .is-hidden-desktop {
        display: block;
    }
}

@media screen and (min-width: 1024px) {

    .is-visible-mobile {
        display: none;
    }
    .fieldsend-2col {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 24px;
    }
    .fieldsend {
        margin-top: 30px;
    }
    .header-section {
        min-height: 776px;
        margin-bottom: 0;
        background-position: 50% 0;
        background-size: contain;
    }
    .header-section::before {
        background-position: 54% 100%;
        background-size: contain;
    }
    .header-logo {
        width: 168px;
        height: 39px;
        margin-top: 83px;
        margin-left: 0;
        opacity: 1;
    }
    .header-logo svg {
        color: #1a1a1a;
    }
    .header-section-img-pc {
        display: block;
        top: 66px;
        bottom: 93px;
        background-image: url('/assets/lan/general/kbj2bzer/ca-light-girl.png');
        background-size: contain;
        background-position: 56% 100%;
    }
    .header-section-img-mob {
        display: none;
    }
    .header-site-body {
        padding: 0;
    }
    .header-title {
        font-size: 48px;
        padding-top: 0;
        margin-bottom: 55px;
        text-align: left;
        color: #1F1F1F;
        letter-spacing: 0.5px;
    }

    .header-side-main {
        justify-content: flex-start;
        padding-top: 93px;
        margin-right: 53%;
    }
    .header-btn .btn {
        max-width: 247px;
        margin-left: 0;
        transform: translateY(0);
    }
    .footer-side {
        padding: 98px 0 35px;
        font-size: 12px;
        line-height: 1.2;
    }
    .footer-side p {
        font-size: 18px;
        margin-bottom: 17px;
        font-weight: 400;
        font-family: 'Open Sans', Sans-Serif;
    }

    .brand-panel {
        max-width: 790px;
        height: 120px;
        margin-top: -96px;
        margin-left: auto;
        margin-right: auto;
        border-bottom: none;
        box-shadow: 0 0 36px rgba(0, 0, 0, 0.15);
        z-index: 1;
    }

    .brand-item {
        font-size: 26px;
        font-weight: 700;
        letter-spacing: 5px;
    }
    .brand-item:not(:last-child)::after {
        width: 6px;
        height: 6px;
        margin: 0 12px;
    }

    /* BENEFITS side */
    .benefit-section {
        padding: 0 0 87px;
    }
    .benefit-section-side {
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 1.3%;
        grid-row-gap: 64px;
        flex-flow: row wrap;
        justify-content: center;
        align-items: stretch;
        padding: 101px 88px 0;
}
    .benefit-block {
        flex-flow: row nowrap;
        max-width: 100%;
        margin-bottom: 0;
        padding: 0 14px 25px;
    }
    .benefit-block-icon {
        min-width: 72px;
        min-height: 72px;
        margin-right: 30px;
        margin-bottom: 0;
    }
    .benefit-block-title {
        margin-bottom: 12px;
        font-size: 22px;
        text-align: left;
        padding-top: 5px;
        letter-spacing: 0.2px;
    }
    .benefit-block-text {
        text-align: left;
        font-size: 18px;
    }
    .benefit-btn-side .btn {
        max-width: 247px;
    }
    /* end BENEFITS side */

    /* CCS side */
    .cc-section {
        padding: 75px 0 0;
    }
    .cc-section-title {
        font-size: 40px;
        color: #262626;
        text-align: left;
        margin-bottom: 70px;
        letter-spacing: 0.5px;
    }
    .cc-section-title div {
        display: inline;
    }
    #dynamic-content-widget .cc-section-filter {
        margin-bottom: 62px;
        justify-content: space-between;
    }
    #dynamic-content-widget .cc-filter-row {
        display: flex;
    }
    #dynamic-content-widget .cc-section-wrapper {
        margin: 0 0 55px;
    }
    #dynamic-content-widget .cc-section-list {
        grid-template-columns: repeat(3, 1fr);
        grid-column-gap: 33px;
    }
    #dynamic-content-widget .fader {
        display: flex;
    }
    #dynamic-content-widget .cc-box-wrap {
        margin: 0;
    }
    #dynamic-content-widget .cc-box-details {
        margin-bottom: 25px;
    }
    /* end CC side */

    /*  TOOLS side */
    .tools-section {
        padding: 133px 0 100px;
    }
    .tools-title {
        font-size: 40px;
        font-weight: 600;
        margin-bottom: 70px;
    }
    .tools-section-area {
        display: grid;
        grid-template-columns: 43% auto;
        grid-column-gap: 20px;
        margin: 0;
        padding-bottom: 40px;
    }
    .tools-block {
        display: flex;
        flex-grow: 0;
        flex-flow: column;
        padding: 0 0 32px 120px;
    }
    .tools-block:not(:last-child)::after {
        left: 43px;
    }
    .tools-block-image {
        display: flex;
    }
    .tools-block:not(:last-child) {
        padding-bottom: 64px;
    }
    .tools-block-icon {
        top: -16px;
        min-width: 76px;
        height: 76px;
    }
    .tools-block-title {
        font-size: 22px;
        margin-bottom: 15px;
    }
    .tools-block-text {
        font-size: 18px;
        padding-right: 35px;
    }
    /* end TOOLS side */

    /* REVIEWS side */
    .reviews-section {
        padding-top: 72px;
    }
    .reviews-section::before {
        background-position: 100% 0;
        background-size: contain;
    }
    .reviews-section-box {
        grid-template-columns: repeat(2, 1fr);
        grid-column-gap: 32px;
        margin: 0 0 67px 0;
    }
    .review-block {
        margin: 0;
        max-width: 583px;
    }
    .review-block-body {
        flex-flow: row nowrap;
        padding: 81px 53px 63px 42px;
        box-shadow: 0 8px 40px rgba(144, 144, 144, 0.2);
    }
    .review-block-img-box {
        left: 40px;
        width: 96px;
        height: 96px;
    }
    .review-block-name {
        font-size: 22px;
    }
    .review-block-info {
        font-size: 18px;
        margin-bottom: 23px;
    }
    .review-block-text {
       font-size: 18px;
    }
    .reviews-section-title {
        font-size: 42px;
        margin-bottom: 15px;
        letter-spacing: normal;
        margin-left: 26px;
    }
    /* end REVIEWS side */

    /*  ABOUT side */
    .about-section {
        padding: 135px 0 80px;
    }
    .about-section-top-blob {
        display: none;
    }
    .about-section-title {
        font-size: 40px;
        letter-spacing: 0.5px;
        text-align: center;
        margin-bottom: 44px;
    }
    .about-block {
        padding-left: 70px;
        padding-bottom: 35px;
        transition: all 0.3s linear 0s;
    }
    .about-block.isOpen {
        padding-left: 60px;
    }
    .about-block:hover {
        cursor: pointer;
    }
    .about-block-heading {
        padding: 35px 35px 0 0;
    }
    .about-block-arrow {
        top: 33px;
        right: 14px;
    }
    .about-block-title {
        font-size: 22px;
    }
    .about-block-body-text {
        padding: 20px 145px 10px 0;
    }
    .about-block-body-text p {
        font-size: 18px;
    }
    /* end ABOUT side */

    /*  FORM side */
    .section-form {
       padding: 0;
    }
    .section-form-bg {
        top: 48px;
        bottom: 75px;
    }
    .section-form-bg::after {
       background-position: 50% 0;
        transform: rotate(0);
    }
    .section-form-body {
        grid-template-columns: 57.5% auto;
        grid-column-gap: 9%;
        align-items: center;
    }
    .section-form-title {
        margin-bottom: 13px;
        font-size: 36px;
        text-align: left;
        order: 2;
    }
    .form {
        margin: 0 0 0 auto;
        padding: 12px 0 55px;
        order: 1;
    }
    .form-body {
        padding: 0 45px;
    }
    .form-disclaimer {
        padding: 39px 45px 32px;
    }
    .form-btn-panel .btn {
    }
    /* end FORM side */
}


/* Modal overlay */
.modal {
  display: none;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0,0,0);
  background-color: rgba(0,0,0,0.4);
}

/* Modal box */
.modal-content {
  color: #000;
  font-weight: 400;
  font-size: 16px;
  background-color: #fefefe;
  margin: 25px auto;
  padding: 30px;
  border: 1px solid #888;
  border-radius: 7px;
  width: 80%;
}

@media screen and (max-width: 768px) {
  .modal-content {
    padding: 20px;
    margin-top: 3%;
	width: 95%;
  }
}

/* Modal close Button */
.modal-close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.modal-close:hover,
.modal-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.modal-scroll-top {
    cursor: pointer;
    border-radius: 4px;
  	position: fixed;
    background: #fff;
    right: 30px;
    bottom: 30px;
    z-index: 1000;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    padding: 7px 14px 10px;
    text-align: center;
}

.modal-scroll-top:hover {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}
.modal-content h2 {
    margin: 0;
    line-height: 1.3;
    color: #000;
}
.modal-content p, .modal-content li {
    font-size: 16px;
    color: #000;
    line-height: 1.7;
    font-weight: 300;
}
.modal-content ul {
    list-style: initial;
    padding-left: 1.5em;
}
.modal-content ol {
    list-style: decimal;
    padding-left: 1.5em;
}
.modal-content h2 {
    margin: 0;
    line-height: 1.3;
    color: #000;
}
.modal-content p, .modal-content li {
    font-size: 16px;
    color: #000;
    line-height: 1.7;
    font-weight: 300;
}
.modal-content ul {
    list-style: initial;
    padding-left: 1.5em;
}
.modal-content ol {
    list-style: decimal;
    padding-left: 1.5em;
}

